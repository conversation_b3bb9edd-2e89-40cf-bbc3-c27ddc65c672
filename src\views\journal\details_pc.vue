<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Star, StarFilled } from '@element-plus/icons-vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import ExpertCard from "@/components/expertCard.vue";
import friendCard from "@/components/friendCard.vue";
import aboutCard from "@/components/aboutCard.vue";
import otherCard from "@/components/otherCard.vue";
import serviceCard from "@/components/serviceCard.vue";
import shareCard from "@/components/shareCard.vue";
import { useSystemStore } from '@/store/system.ts'
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import {safePush} from "@/utils/router.ts";
import { contentDetail, toggleCollect, createConsultation, getCaptcha } from '@/api/common.ts'
import { useRoute, useRouter } from 'vue-router'
import ShareCard from "@/components/shareCard.vue";
import { ElMessage, type FormInstance } from 'element-plus'
const systemStore = useSystemStore()
const route = useRoute();
const router = useRouter();

const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: []
})

// 咨询弹窗相关
const dialogConsultationVisible = ref(false)
const consultationFormRef = ref<FormInstance>()

// 咨询表单数据 - 按照数据库字段命名
const consultationForm = ref({
  content_id: '', // 服务ID
  name: '',       // 姓名
  tel: '',        // 电话
  email: '',      // 邮箱
  specialization: '', // 专业方向
  captcha: ''
})

// 验证码相关
const captchaId = ref('')
const captchaUrl = ref('')

// 咨询表单验证规则 - 按照数据库字段命名
const consultationRules = ref({
  name: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  tel: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  email: [
    {
      pattern: /^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度为4-6位', trigger: 'blur' }
  ]
})

const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
      ElMessage.success(res.msg)
  })
}

// 生成验证码ID
const generateCaptchaId = () => {
  return 'captcha_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 刷新验证码
const refreshCaptcha = async () => {
  captchaId.value = generateCaptchaId()
  console.log('PC端刷新验证码ID:', captchaId.value)

  try {
    const response = await getCaptcha(captchaId.value)
    console.log('PC端验证码响应:', response)
    console.log('PC端响应数据类型:', typeof response.data)
    console.log('PC端响应数据:', response.data)

    // response.data 是blob数据
    const blob = response.data
    captchaUrl.value = URL.createObjectURL(blob)
    console.log('PC端验证码URL生成成功:', captchaUrl.value)
    console.log('PC端captchaUrl.value 当前值:', captchaUrl.value)
  } catch (error) {
    console.error('PC端获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请重试')
  }
}

// 打开咨询弹窗
const handleConsultation = () => {
  dialogConsultationVisible.value = true
  consultationFormRef.value?.resetFields()
  // 设置服务ID
  consultationForm.value.content_id = (info.value.detail as any).id
  // 生成验证码
  refreshCaptcha()
}

// 提交咨询表单
const submitConsultation = async () => {
  const formEl = consultationFormRef.value
  if (!formEl) return

  await formEl.validate((valid, fields) => {
    if (valid) {
      dialogConsultationVisible.value = false
      createConsultation({
        content_id: consultationForm.value.content_id, // 服务ID
        name: consultationForm.value.name,              // 姓名
        tel: consultationForm.value.tel,                // 电话
        email: consultationForm.value.email,            // 邮箱
        specialization: consultationForm.value.specialization, // 专业方向
        captchaId: captchaId.value,
        captcha: consultationForm.value.captcha
      }).then(res => {
        ElMessage.success(res.msg)
        // 跳转到用户中心的咨询列表
        router.push('/user/consultation')
      }).catch(err => {
        ElMessage.error(err.msg)
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}

const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="introduce">
      <div class="container">
        <div class="box bg-white flex justify-between">
          <div class="left hidden">
            <img :src="isHttpOrHttps(info.detail.cover_image)" alt="">
          </div>
          <div class="right flex-1">
            <div class="title flex align-center">
              <div class="name">
                <span>{{info.detail.title}}</span>
              </div>
            </div>
            <div class="descs">
              <div class="part flex align-center">
                <div class="name">
                  <span>学科领域</span>
                </div>
                <div class="value">
                  <span>{{info.detail.big_type_name + '-' + info.detail.small_type_name }}</span>
                </div>
              </div>
              <div class="part flex align-center">

              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>期刊类型</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_type ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>数据库</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_datatype ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>ISSN</span>
                </div>
                <div class="value">
                  <span>--</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>中科院</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_zky ?? '--'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>影响因子</span>
                </div>
                <div class="value">
                  <span>{{info.detail.impact_factor ?? '--'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>JCR</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_jcr ?? '--'}}</span>
                </div>
              </div>
            </div>
            <shareCard :title="info.detail.title" :image="isHttpOrHttps(info.detail.cover_image)">
              <div class="tool">
                <el-button type="primary" @click="handleConsultation">立即投稿</el-button>
                <el-button color="#EEAF3D" :icon="info.detail?.is_collected ? StarFilled : Star" @click="handleCollect">收藏</el-button>
              </div>
            </shareCard>
          </div>
        </div>
      </div>
    </div>
    <div class="contents">
      <div class="container flex justify-between">
        <div class="info flex-1">
          <div class="content bg-white">
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>期刊简介</span>
            </div>
            <div class="values" v-html="info.detail.summary ?? '-'"></div>
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>征稿主题</span>
            </div>
            <div class="values" v-html="info.detail.journal_data ?? '-'"></div>
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>参考周期</span>
            </div>
            <div class="values" v-html="info.detail.paper_requirements ?? '-'"></div>
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>投稿须知</span>
            </div>
            <div class="values" v-html="info.detail.submission_guidelines ?? '-'"></div>
          </div>
          <div class="about" v-if="info.related.services?.length">
            <aboutCard title="热门服务" :icon="getAssetsFile('common/icon_service.png')" moreUrl="/service">
              <div class="items">
                <serviceCard :info="item" v-for="(item, index) in info.related.services" :key="index" />
              </div>
            </aboutCard>
          </div>
          <div class="about" v-if="info.related.experts?.length">
            <aboutCard title="相关专家" :icon="getAssetsFile('common/icon_zj.png')" moreUrl="/expert">
              <div class="items">
                <ExpertCard :info="item" v-for="(item, index) in info.related.experts" :key="index" />
              </div>
            </aboutCard>
          </div>
        </div>
        <div class="slider">
          <div class="card">
            <friendCard />
          </div>
          <div class="card">
            <otherCard title="相关推荐" :icon="getAssetsFile('common/icon_zan.png')" more-url="/expert">
              <div class="users">
                <div class="user cursor-pointer flex flex-column align-center" v-for="(item, index) in info.recommended" :key="index" @click="safePush({path:'/journal/details',query:{id:item.id}})">
                  <div class="avatar flex">
                    <img :src="isHttpOrHttps(item.cover_image)" alt="">
                  </div>
                  <div class="name">
                    <span>{{item.title}}</span>
                  </div>
                </div>
              </div>
            </otherCard>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />

    <!-- 咨询弹窗 -->
    <el-dialog
        v-model="dialogConsultationVisible"
        width="800"
        title="投稿申请"
        class="consultationFormPop"
        :show-close="false"
    >
      <div class="consultation-form">
        <div class="consultation-tip">
          请填写您的联系方式，我们的专业导师将在二十四小时内联系您。
        </div>
        <el-form ref="consultationFormRef" :model="consultationForm" :rules="consultationRules" label-position="left" label-width="auto">
          <el-form-item label="联系人" prop="name">
            <el-input v-model="consultationForm.name" placeholder="请输入联系人" />
          </el-form-item>
          <el-form-item label="电话" prop="tel">
            <el-input v-model="consultationForm.tel" placeholder="请输入电话" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="consultationForm.email" placeholder="请输入邮箱（选填）" />
          </el-form-item>
          <el-form-item label="专业方向">
            <el-input v-model="consultationForm.specialization" placeholder="请输入专业方向（选填）" />
          </el-form-item>
          <el-form-item label="验证码" prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="consultationForm.captcha"
                placeholder="请输入验证码"
                style="width: 200px; margin-right: 10px;"
              />
              <div
                class="captcha-image"
                @click="refreshCaptcha"
                style="width: 120px; height: 40px; border: 1px solid #dcdfe6; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; background: #f5f7fa;"
              >
                <img v-if="captchaUrl" :src="captchaUrl" alt="验证码" style="max-width: 100%; max-height: 100%;" />
                <span v-else style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">点击获取</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitConsultation">提交咨询</el-button>
            <el-button @click="dialogConsultationVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .introduce{
    .container{
      .box{
        margin: px2rem(24) 0 px2rem(20);
        box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
        border-radius: px2rem(16);
        padding: px2rem(36) px2rem(24);
        box-sizing:  border-box;
        .left{
          width: px2rem(173);
          height: px2rem(230);
          img{
            width: 100%;
          }
        }
        .right{
          margin-left: px2rem(20);
          .title{
            .name{
              font-size: px2rem(20);
              color: #1F1F1F;
              line-height: 1;
              font-weight: 600;
            }
          }
          .descs{
            margin-top: px2rem(16);
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: px2rem(12) 0;
            .part{
              font-size: px2rem(16);
              .name{
                color: #93999A;
                width: px2rem(64);
              }
              .value{
                color: #1F1F1F;
                margin-left: px2rem(16);
              }
            }
          }
          .tool{
            .el-button{
              &:first-child{
                width: px2rem(182);
                height: px2rem(52);
                font-size: px2rem(20);
                border-radius: px2rem(27);
              }
              &:last-child{
                background-color: transparent;
                width: px2rem(145);
                height: px2rem(52);
                font-size: px2rem(20);
                border-radius: px2rem(26);
                color: #EEAF3D;
              }
            }
          }
        }
      }
    }
  }
  .contents{
    .container{
      .info{
        margin-right: px2rem(20);
        .content{
          box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
          border-radius:  px2rem(16);
          padding: 0 px2rem(20) px2rem(24);
          box-sizing: border-box;
          .name-bar{
            color: #191B1F;
            font-weight: 600;
            font-size: px2rem(24);
            padding: px2rem(30) 0;
            box-sizing: border-box;
            img{
              width: px2rem(39);
              height: px2rem(27);
              margin-right: px2rem(10);
            }
          }
          .values{
            color: #525252;
            font-size: px2rem(18);
            line-height: px2rem(25);
          }
          .card{
            background-color: #3B78C8;
            margin-top: px2rem(40);
            padding: px2rem(20);
            box-sizing: border-box;
            .parts{
              .part{
                padding: px2rem(60) 0;
                box-sizing: border-box;
                .avatar{
                  width: px2rem(120);
                  height: px2rem(140);
                  border-radius: px2rem(6);
                  overflow: hidden;
                  img{
                    width: 100%;
                  }
                }
                .name{
                  color: #191B1F;
                  font-size: px2rem(24);
                  margin: px2rem(18) 0 px2rem(20);
                  font-weight: 500;
                }
                .concat{
                  color: #909090;
                  font-size: px2rem(16);
                }
                .qrcode{
                  width: px2rem(160);
                  height: px2rem(160);
                  border-radius: px2rem(6);
                  overflow: hidden;
                  img{
                    width: 100%;
                  }
                }
                .tips{
                  margin-top: px2rem(20);
                  font-size: px2rem(24);
                  color: #191B1F;
                }
              }
            }
          }
        }
        .about{
          margin-top: px2rem(60);

          .items{
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: px2rem(16);
          }
        }
      }
      .slider{
        width: px2rem(353);
        .card{
          &+.card{
            margin-top: px2rem(20);
          }
          .users{
            .user{
              background: #F4F6F9;
              box-shadow: 0 px2rem(2) px2rem(8) 0 rgba(0,66,190,0.08);
              border-radius: px2rem(6);
              padding: px2rem(26) px2rem(22);
              box-sizing: border-box;
              .avatar{
                width: px2rem(171);
                height: px2rem(229);
                img{
                  width: 100%;
                }
              }
              .name{
                color: #1F1F1F;
                margin-top: px2rem(16);
                font-size: px2rem(16);
              }
              &+.user{
                margin-top: px2rem(20);
              }

            }
          }
        }
      }
    }
  }
  .empty{
    margin-top: px2rem(100);
  }
}

// 咨询弹窗样式
:deep(.consultationFormPop) {
  .el-dialog__body {
    padding: px2rem(20) px2rem(30);
  }

  .consultation-form {
    .consultation-tip {
      margin-bottom: px2rem(20);
      color: #666;
      font-size: px2rem(14);
      line-height: 1.5;
    }

    .captcha-container {
      display: flex;
      align-items: center;
    }

    .el-form-item {
      margin-bottom: px2rem(20);
    }

    .el-button {
      margin-right: px2rem(10);
    }
  }
}
</style>