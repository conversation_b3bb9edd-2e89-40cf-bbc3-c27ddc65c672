<script setup lang="ts">
import {safePush} from "@/utils/router.ts";
import {isHttpOrHttps} from "@/utils/utils.ts";
const { info, isFlex, imgWidth, imgHeight } = defineProps({
  info: {
    type: Object,
    required: true,
  },
  isFlex: {
    type: Boolean,
    required: false,
    default: false,
  },
  imgWidth: {
    type: Number,
    required: false,
    default: 47
  },
  imgHeight: {
    type: Number,
    required: false,
    default: 63
  }
})
</script>

<template>
  <div class="item hidden cursor-pointer bg-white" :class="{flex: isFlex, shadow: !isFlex, border: isFlex}" @click="safePush({path: '/journal/details', query:{id:info.id}})">
    <div class="picimage" :style="{width: imgWidth+'px', height: imgHeight+'px'}">
      <el-image :src="isHttpOrHttps(info.cover_image)" fit="cover"></el-image>
    </div>
    <div class="info flex-1"  :style="{marginLeft: isFlex ? '5px' : 0, marginTop: isFlex ? 0 : '5px'}">
      <div class="title row1">
        <span v-html="info.title"></span>
      </div>
      <div class="part flex" :style="{marginTop: isFlex ? '5px' : '5px'}">
        <div class="_name">
          <span>学科领域</span>
        </div>
        <div class="value flex-1">
          <span>{{info.big_type_name}} - {{info.small_type_name}}</span>
        </div>
      </div>
      <div class="part flex" :style="{marginTop: isFlex ? '5px' : '5px'}">
        <div class="_name">
          <span>期刊类型</span>
        </div>
        <div class="value flex-1">
          <span>{{info.journal_type}}</span>
        </div>
      </div>
      <div class="part flex" :style="{marginTop: isFlex ? '5px' : '5px'}">
        <div class="_name">
          <span>数据库</span>
        </div>
        <div class="value flex-1">
          <span>{{info.journal_datatype}}</span>
        </div>
      </div>
      <div class="part flex" :style="{marginTop: isFlex ? '5px' : '5px'}">
        <div class="_name">
          <span>中科院</span>
        </div>
        <div class="value flex-1">
          <span>{{info.journal_zky}}</span>
        </div>
      </div>
      <div class="part flex" :style="{marginTop: isFlex ? '5px' : '5px'}">
        <div class="_name">
          <span>影响因子</span>
        </div>
        <div class="value flex-1">
          <span>{{info.impact_factor}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  padding: 10px;
  box-sizing: border-box;
  &.shadow{
    box-shadow: 0 1px 12px 0 rgba(0, 66, 190, 0.14);
  }
  &.border{
    border: 1px solid #CCCCCC;
  }
  .picimage {
    width: 47px;
    height: 63px;
    margin: 0 auto;
    .el-image{
      width: 100%;
      height: 100%;
    }
  }
  .info{
    .title {
      color: #1F1F1F;
      font-size: 12px;
      font-weight: 600;
    }

    .part {
      ._name {
        font-size: 12px;
        color: #93999A;
        width: 48px;
        margin-right: 5px;
      }

      .value {
        font-size: 12px;
        word-break: break-all;
        color: #535871;
      }
    }
  }
}
</style>