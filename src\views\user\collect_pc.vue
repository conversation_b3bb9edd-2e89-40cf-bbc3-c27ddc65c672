<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import userInfoCard from "@/components/userInfoCard.vue";
import userSliderCard from "@/components/userSliderCard.vue";
import serviceCard from "@/components/serviceCard.vue";
import expertCard from "@/components/expertCard.vue";
import journalCard from '@/components/journalCard.vue';
import storiesCard from "@/components/storiesCard.vue";
import paginate from "@/components/paginate.vue";
import { myCollects } from '@/api/common.ts'

const tabs = ref([
  {name: '全部', value: '', row: 3},
  {name: '学术科研服务', value: 'SERVICE', row: 3},
  {name: '专家智库', value: 'EXPERT', row: 3},
  {name: '学术期刊', value: 'JOURNAL', row: 3},
  {name: '学术头条', value: 'HEADLINE', row: 2},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref([])
const total = ref(0)
const getMyCollects = () => {
  myCollects({
    content_type: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value
  }).then(res=>{
    total.value = res.data.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getMyCollects()
}
watch(tabCur, (val) => {
  page.value = 1
  list.value = []
  getMyCollects()
},{
  deep: true
})
onMounted(()=>{
  getMyCollects()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="userinfo">
      <div class="container">
        <userInfoCard />
      </div>
    </div>
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="sliders">
          <userSliderCard />
        </div>
        <div class="box bg-white flex-1">
          <div class="tabs flex align-center">
            <div class="tab cursor-pointer" :class="{active: tabCur === index}" @click="tabCur = index" v-for="(item, index) in tabs" :key="index">
              <span>{{item.name}}</span>
            </div>
          </div>
          <div class="items" :style="{gridTemplateColumns: `repeat(${tabs[tabCur].row}, 1fr)`}">
            <div class="part" v-for="(item, index) in list" :key="index">
              <template v-if="item.content.content_type === 'SERVICE'">
                <serviceCard :info="item.content" />
              </template>
              <template v-else-if="item.content.content_type === 'EXPERT'">
                <expertCard :info="item.content" />
              </template>
              <template v-else-if="item.content.content_type === 'JOURNAL'">
                <journalCard :info="item.content" />
              </template>
              <template v-else-if="item.content.content_type === 'HEADLINE'">
                <storiesCard :info="item.content" />
              </template>
            </div>
          </div>
          <div class="paginates">
            <paginate :limit="limit" :total="total" @change="changePage" />
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .userinfo{
    margin: px2rem(20) 0;
  }
  .boxs{
    .sliders{
      width: px2rem(238);
    }
    .box{
      margin-left: px2rem(20);
      border-radius: px2rem(16);
      .tabs{
        padding: px2rem(24) px2rem(40) px2rem(11);
        box-sizing: border-box;
        border-bottom: px2rem(1) solid #E9E9E9;
        .tab{
          color: #535871;
          font-size: px2rem(18);
          border-bottom: px2rem(3) solid transparent;
          line-height: 1;
          padding-bottom: px2rem(5);
          &+.tab{
            margin-left: px2rem(50);
          }
          &.active{
            color: $maincolor;
            border-color: $maincolor;
          }
        }
      }
      .items{
        padding: px2rem(30) px2rem(24);
        box-sizing: border-box;
        display: grid;
        grid-gap: px2rem(20);
      }
    }
  }
  .empty{
    height: px2rem(100);
  }
}
</style>