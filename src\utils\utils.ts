import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import { useSystemStore } from '@/store/system.ts'
dayjs.extend(duration)

/**
 * 导入图片
 * @param url 相对路径，如 'home/1.png' 或 'video/1.png'
 */
const imageModules = import.meta.glob('../assets/images/**/*', { eager: true, import: 'default' });

export const getAssetsFile = (url: string): string => {
  const key = `../assets/images/${url}`;
  return (imageModules[key] as unknown as string) || '';
}

/**
 * px转rem
 * @param px
 */
export const getRemSize = (px: number) => {
  const htmlFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
  return (px / 16) * htmlFontSize;
}

/**
 * 获取星期
 * @param date
 * @param day
 * @param isChinese
 */
export const getWeekFromDate = (date:string, day:number, isChinese:boolean) => {
  const _date = dayjs(date).format('YYYY-MM'+'-'+day);
  const chinese = ['日','一','二','三','四','五','六'];
  return isChinese ? chinese[dayjs(_date).day()] : dayjs(_date).day()
}

/**
 * 获取格式化后的日期
 */
export const getTimeByTimestamp = (timestamp: string, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(timestamp).format(format);
}

/**
 * 计算两个时间之间的时差（兼容超过24小时的时分秒格式）
 * @param time1 第一个时间（可以是Date对象、时间字符串或时间戳）
 * @param time2 第二个时间（可以是Date对象、时间字符串或时间戳）
 * @returns 返回格式化的时差字符串，如 "36:30:45" 表示36小时30分45秒
 */
export function getLongTimeDifference(time1: Date | string | number, time2: Date | string | number): string {
  // 确保time1和time2是dayjs对象
  const d1 = dayjs(time1)
  const d2 = dayjs(time2)

  // 计算时间差（毫秒）
  const diff = Math.abs(d1.diff(d2))

  // 转换为duration对象
  const dur = dayjs.duration(diff)

  // 计算总小时、分钟、秒数
  const totalHours = Math.floor(dur.asHours())
  const minutes = dur.minutes()
  const seconds = dur.seconds()

  // 补零格式化
  const padZero = (num: number): string => num.toString().padStart(2, '0')

  return `${totalHours}:${padZero(minutes)}:${padZero(seconds)}`
}

/**
 * 计算两个时间之间的时差（返回时分秒对象，兼容超过24小时）
 * @param time1 第一个时间
 * @param time2 第二个时间
 * @returns 返回包含总小时、分钟、秒数的对象
 */
export function getLongTimeDifferenceObject(time1: Date | string | number, time2: Date | string | number): {
  totalHours: number
  hours: number
  minutes: number
  seconds: number
  days: number
} {
  const d1 = dayjs(time1)
  const d2 = dayjs(time2)
  const diff = Math.abs(d1.diff(d2))
  const dur = dayjs.duration(diff)

  const totalHours = Math.floor(dur.asHours())
  const days = Math.floor(totalHours / 24)
  const hours = totalHours % 24

  return {
    totalHours,  // 总小时数
    hours,       // 不足24小时的部分
    minutes: dur.minutes(),
    seconds: dur.seconds(),
    days         // 天数
  }
}

/**
 * 计算指定时间距离现在的分钟数
 * @param time 指定的时间（可以是Date对象、时间字符串或时间戳）
 * @returns 返回距离现在的分钟数（向下取整），如果时间为null则返回'-'
 */
export function getMinutesFromNow(time: Date | string | number | null): number | string {
  if (time === null) return '-'
  const now = dayjs()
  const targetTime = dayjs(time)
  const diff = Math.abs(now.diff(targetTime))
  const minutes = Math.floor(diff / (1000 * 60))
  return minutes
}

/**
 * 获取近 N 日的开始和结束日期
 * @param days 天数，例如 7 或 30
 * @param format 日期格式，默认 'YYYY-MM-DD'
 * @returns { startDate: string, endDate: string }
 */
export function getRecentDateRange(days: number, format: string = 'YYYY-MM-DD'): {
  startDate: string,
  endDate: string
} {
  const endDate = dayjs().format(format)
  const startDate = dayjs().subtract(days - 1, 'day').format(format)
  return { startDate, endDate }
}

/**
 * 检查字符串是否以 http:// 或 https:// 开头
 * @param {string} url 要检查的字符串
 * @returns {boolean} 如果以 http:// 或 https:// 或 // 开头返回 true，否则返回 false
 */
export function isHttpOrHttps(url: string) {
  const systemStore = useSystemStore();
  const result = /^(https?:)?\/\//i.test(url)
  return result ? url : systemStore.site.cdnUrl + url;
}

/**
 * 跳出外站url
 * @param url
 */
export function jumpOutWeb(url: string) {
  if (!url) {
    return
  }
  window.open(url, '_blank');
}

/**
 * 分享到微博
 */
export const shareToWeibo = (title: string, image: string = '') => {
  title = encodeURIComponent(title);
  const url = encodeURIComponent(window.location.href);
  const pic = encodeURIComponent(image);
  const shareUrl = `https://service.weibo.com/share/share.php?title=${title}&url=${url}&pic=${pic}`;
  window.open(shareUrl, "_blank");
};

/**
 * 分享到qq
 */
export const shareToQQ = (title: string, image: string) => {
  title = encodeURIComponent(title);
  const url = encodeURIComponent(window.location.href);
  const desc = encodeURIComponent("");
  const summary = encodeURIComponent("");
  const pic = encodeURIComponent(image);
  const shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${title}&desc=${desc}&summary=${summary}&pics=${pic}`;
  window.open(shareUrl, "_blank");
};