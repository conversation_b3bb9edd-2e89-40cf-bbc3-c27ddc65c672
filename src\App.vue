<script setup lang="ts">
import { onBeforeMount } from 'vue'
import { initialization } from '@/api/common.ts'
import { loginThird } from '@/api/login.ts'
import { useSystemStore } from '@/store'
import { useUserInfoStore } from '@/store/userInfo.ts'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { safePush } from '@/utils/router'
const router = useRouter()
const systemStore = useSystemStore()
const userInfoStore = useUserInfoStore()
const initializationSystem = () => {
  initialization({
    requiredLogin: 0
  }).then((res) => {
    systemStore.setMenus(res.data.menus)
    systemStore.setOpenMemberCenter(res.data.openMemberCenter)
    systemStore.setRules(res.data.rules)
    systemStore.setSite(res.data.site)
    systemStore.setUserInfo(res.data.userInfo)
    systemStore.setWxQrcode(res.data.wx_qrcode)
    systemStore.setKefuQrcode(res.data.kefu_qrcode)
    systemStore.setYhxy(res.data.yhxy)
    systemStore.setYszc(res.data.yszc)
    systemStore.setBqsm(res.data.bqsm)
    systemStore.setKefuUrl(res.data.kefu_url)
    systemStore.setBaseEmal(res.data.base_emal)
    systemStore.setBaseJobtime(res.data.base_jobtime)
    systemStore.setBaseTel(res.data.base_tel)
    systemStore.setHiddenNav(res.data.site.hiddenNav)
    systemStore.setQkImage(res.data.site.qk_image)
  })
}
const getParams = (url: string) => {
  let urlParams: Record<string, string> = {};
  if (url.indexOf('?') != -1) {
    const queryString = url.split('?')[1];
    const pairs = queryString.split('&');
    for (let i = 0; i < pairs.length; i++) {
      const pair = pairs[i];
      const firstEqualIndex = pair.indexOf('=');
      if (firstEqualIndex !== -1) {
        const key = pair.substring(0, firstEqualIndex);
        const value = pair.substring(firstEqualIndex + 1);
        urlParams[key] = decodeURI(value);
      }
    }
  }
  console.log('urlParams', urlParams);
  if (urlParams.code && urlParams.state) {
    loginThird({
      code: urlParams.code,
      state: urlParams.state,
    }).then(res=>{
      ElMessage.success(res.msg)
      userInfoStore.setUserInfo(res.data.userInfo)
      userInfoStore.setIsLogin(true)
      const newUrl = window.location.origin + '/#/home';
      window.history.replaceState({}, '', newUrl);
      setTimeout(() => {
        router.replace('/home');
        safePush('/user')
      }, 0);
    })
  }
}
onBeforeMount(() => {
  getParams(window.location.href);
  initializationSystem()
})
</script>

<template>
  <router-view />
</template>

<style lang="scss">
*{
  padding: 0;
  margin: 0;
}
.el-overlay{
  .el-overlay-dialog{
    .el-dialog{
      border-radius: px2rem(24);
      padding: px2rem(50) px2rem(95);
      .el-dialog__header{
        padding-bottom: px2rem(46);
        box-sizing:  border-box;
        .el-dialog__title{
          font-size: px2rem(20);
          color: #191B1F;
        }
        .el-dialog__headerbtn{
          top: px2rem(-65);
          right: px2rem(-20);
          .el-icon{
            color: #fff;
            background-color: #a3a3a3;
            border-radius: 50%;
            width: px2rem(34);
            height: px2rem(34);
          }
        }
      }
      .el-dialog__body{
        padding: 0;
      }
      &.sharePop{
        padding: px2rem(10);
        border-radius: px2rem(10);
        .el-dialog__header{
          padding-top: px2rem(20);
          padding-bottom: px2rem(10);
          text-align: center;
        }
      }
      &.buyPop{
        padding: 0 0 px2rem(50) 0;
        border-radius: px2rem(10);
        .el-dialog__header{
          padding-top: px2rem(50);
          padding-bottom: px2rem(30);
          text-align: center;
          font-size: px2rem(24);
          line-height: 1;
        }
      }
      &.buyFormPop{
        padding: 0 0 px2rem(50) 0;
        border-radius: px2rem(10);
        .el-dialog__header{
          padding-top: px2rem(35);
          padding-bottom: px2rem(20);
          text-align: center;
          font-size: px2rem(24);
          line-height: 1;
        }
      }
      &.changeMobilePop{

      }
      &.studyPop{
        border-radius: 8px;
        background: linear-gradient( 180deg, #FFFFFF 0%, #E1FFED 100%);
        padding: px2rem(50) 0;
        .el-dialog__header{
          display: none;
        }
      }
      &.studyMobilePop{
        background: linear-gradient( 180deg, #FFFFFF 0%, #E1FFED 100%);
        border-radius: 6px;
        padding: 38px 0;
        width: calc(100% - 30px);
        .el-dialog__header{
          display: none;
        }
      }
      &.loginTexts{
        border-radius: 6px;
        padding: 20px 20px 10px;
        .el-dialog__header{
          padding-bottom: 20px;
        }
        .content{
          height: 60vh;
          overflow-y: auto;
        }
      }
    }
  }
  .mobile-buy-pop{
    background: #F5F7FA;
    .el-drawer__header{
      margin-bottom: 0;
      height: 54px;
      padding: 12px 15px;
      box-sizing: border-box;
      justify-content:space-between;
      background-color: #fff;
      border-bottom: 1px solid #F5F7FA;
      .el-icon{
        display: inline-block;
        flex: unset;
      }
      .title{
        font-size: 16px;
        color: #0A0A0B;
        max-width: 300px;
      }
    }
    .el-drawer__body {
      background: #F5F5F5;
      display: flex;
      flex-direction: column;
      padding: 0;
      .buy-info{
        .pay-box{
          margin-top: 11px;
          padding: 20px 15px;
          box-sizing: border-box;
          .title{
            font-size: 16px;
            color: #1F1F1F;
            font-weight: 500;
          }
          .price{
            margin-top: 20px;
            color: #E64F26;
            font-weight: 600;
            span{
              &:nth-child(1){
                font-size: 18px;
              }
              &:nth-child(2){
                font-size: 30px;
              }
            }
          }
          .unit{
            color: #93999A;
            font-size: 12px;
            font-weight: 400;
          }
          .pays{
            .pay{
              padding: 12px 15px;
              box-sizing: border-box;
              border-radius: 8px;
              border: 1px solid #F5F7FA;
              &.active{
                background-color: #F5F7FA;
              }
              &+.pay{
                margin-top: 20px;
              }
              .left{
                color: #191B1F;
                font-size: 14px;
                font-weight: 500;
                img{
                  width: 20px;
                  margin-right: 10px;
                }
              }
              .right{
                img{
                  width: 16px;
                }
              }
            }
          }
          .el-form{
            .el-form-item{
              margin-bottom: 16px;
              &:last-child{
                margin-bottom: 0;
              }
              .el-form-item__label-wrap{
                .el-form-item__label{
                  color: #535871;
                  font-size: 14PX;
                  padding-right: 26px;
                  height: 44px;
                  line-height: 44px;
                }
              }
              .el-form-item__content{
                .el-form-item__error{
                  font-size: 12px;
                }
                .el-input{
                  .el-input__wrapper{
                    box-shadow: unset;
                    background-color: #F7FAFE;
                    padding: 0 9px;
                    border-radius: 8px;
                    .el-input__inner{
                      height: 44px;
                      font-size: 14px;
                    }
                  }
                }
              }
            }
          }
        }
        .buttons{
          margin: 30px;
          .el-button{
            width: 100%;
            border-radius: 22px;
            font-size: 14px;
            height: 44px;
            line-height: 44px;
          }
        }
      }
    }
  }
  .service-filters-popup, .journal-filters-popup{
    .el-drawer__header{
      margin-bottom: 0;
      height: 54px;
      padding: 12px 15px;
      box-sizing: border-box;
      justify-content:space-between;
      background-color: #fff;
      border-bottom: 1px solid #F5F7FA;
      .el-icon{
        display: inline-block;
        flex: unset;
      }
      .title{
        font-size: 16px;
        color: #0A0A0B;
        max-width: 300px;
      }
    }
    .el-drawer__body{
      background: #F5F5F5;
      display: flex;
      flex-direction: column;
      padding: 0;
      .filter{
        .slide{
          width: 92px;
          .option{
            padding: 15px 0;
            box-sizing: border-box;
            font-size: 14px;
            color: #000000;
            font-weight: 400;
            &.active{
              background-color: #fff;
              font-weight: 500;
            }
          }
        }
        .box{
          padding: 15px;
          box-sizing: border-box;
          .item{
            color: #000000;
            .title{
              font-size: 14px;
              padding-bottom: 10px;
              box-sizing: border-box;
            }
            .children, .select{
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              grid-gap: 12px;
              margin-bottom: 20px;
              .child{
                font-size: 12px;
                background-color: #F5F5F5;
                padding: 10px 0;
                box-sizing: border-box;
                border: 1px solid transparent;
                &.active{
                  background-color: #fff;
                  color: $maincolor;
                  border-color: $maincolor;
                }
              }
            }
            .range, .input{
              margin-bottom: 20px;
              .el-input{
                .el-input__wrapper{
                  box-shadow: unset;
                  border: 1px solid #7280A7;
                  height: 34px;
                  padding: 0 11px;
                  font-size: 12px;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
      .el-anchor{
        background-color: transparent;
        .el-anchor__marker{
          display:  none;
        }
        .el-anchor__list{
          display: flex;
          padding-left: 0;
          .slide{
            width: 92px;
            .option{
              padding: 15px 0;
              box-sizing: border-box;
              .el-anchor__link{
                font-size: 14px;
                color: #000000;
                font-weight: 400;
              }
              &.active{
                background-color: #fff;
                .el-anchor__link{
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
    .el-drawer__footer{
      padding: 12px 15px 12px;
      padding: 12px 15px calc(constant(safe-area-inset-bottom) + 12px);
      padding: 12px 15px calc(env(safe-area-inset-bottom) + 12px);
      box-sizing: border-box;
      .buttons{
        .button{
          font-size: 16px;
          color: #5E5E5E;
          padding: 10px 0;
          box-sizing: border-box;
          border-radius: 2px;
          &+.button{
            margin-left: 11px;
          }
          &.cancel{
            background: #F5F5F5;
            color: #5E5E5E;
          }
          &.confirm{
            background: $maincolor;
            color: #fff;
          }
        }
      }
    }
  }
}
#wechat_qrcode_container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: px2rem(160);
  height: px2rem(160);
  position: relative;
  overflow: hidden;

  iframe {
    display: block;
    margin: 0 auto;
    width: 100% !important;
    height: 100% !important;
    min-width: 0 !important;
    min-height: 0 !important;
    border: none;
    background: transparent;
  }
}
</style>
