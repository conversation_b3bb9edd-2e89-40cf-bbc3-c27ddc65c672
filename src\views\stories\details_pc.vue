<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import {getAssetsFile, shareToQQ, shareToWeibo} from "@/utils/utils.ts";
import {safePush} from "@/utils/router.ts";
import OtherCard from "@/components/otherCard.vue";
import AdCard from "@/components/adCard.vue";
import { contentDetail, toggleCollect, toggleLike } from '@/api/common.ts'
import QRCode from "qrcode";
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const route = useRoute();
const router = useRouter();

// 定义接口类型
interface DetailInfo {
  id: string | number
  title: string
  author: string
  publish_date: string
  content: string
  like_count: number
  collect_count: number
  is_collected: boolean
  is_liked: boolean
}

interface HotTag {
  name: string
  count: number
}

interface Article {
  id: number
  title: string
  publish_date: string
  click_count?: number
}

interface NavigationArticle {
  id: number
  title: string
  cover_image: string
  publish_date: string
}

interface InfoData {
  detail: DetailInfo
  related: {
    services: any[]
    experts: any[]
    journals: any[]
  }
  recommended: any[]
  breadcrumb: any[]
  hotTags: HotTag[]
  latestArticles: Article[]
  hotArticles: Article[]
  prevNext: {
    prev: NavigationArticle | null
    next: NavigationArticle | null
  }
}

const info = ref<InfoData>({
  detail: {
    id: '',
    title: '',
    author: '',
    publish_date: '',
    content: '',
    like_count: 0,
    collect_count: 0,
    is_collected: false,
    is_liked: false
  },
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: [],
  hotTags: [],
  latestArticles: [],
  hotArticles: [],
  prevNext: {
    prev: null,
    next: null
  }
})

const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
    ElMessage.success(res.msg)
  })
}

const handleLike = () => {
  toggleLike({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_liked = res.data.is_liked
    info.value.detail.like_count = res.data.like_count
    ElMessage.success(res.msg)
  })
}

const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}

const dialogVisible = ref(false);
const qrCode = ref('')
const generateQRCode = async (): Promise<string> => {
  return await QRCode.toDataURL(location.href);
};

const shareToWechat = async () => {
  qrCode.value = await generateQRCode()
  dialogVisible.value = true
}

const goToArticle = (id: number) => {
  safePush({
    path: '/stories/details',
    query: { id }
  })
}

watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})

onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="contents">
      <div class="container flex justify-between">
        <div class="info flex-1">
          <div class="content bg-white">
            <div class="title">
              <span>{{info.detail.title}}</span>
            </div>
            <div class="tips flex align-center">
              <div class="author">
                <span>{{info.detail.author}}</span>
              </div>
              <div class="time">
                <span>{{info.detail.publish_date}}</span>
              </div>
              <div class="zan flex align-center">
                <img src="@/assets/images/common/icon_zans.png" alt="">
                <span>{{info.detail.like_count}}</span>
              </div>
            </div>
            <div class="html" v-html="info.detail.content"></div>
            <div class="tools flex align-center justify-center">
              <el-button class="text-white" :class="{active: info.detail?.is_collected}" @click="handleCollect">
                <img src="@/assets/images/common/icon_star_1.png" alt="">
                <span>收藏 {{info.detail.collect_count}}</span>
              </el-button>
              <el-button class="text-white" :class="{active: info.detail?.is_liked}" @click="handleLike">
                <img src="@/assets/images/common/icon_zan_1.png" alt="">
                <span>点赞 {{info.detail.like_count}}</span>
              </el-button>
            </div>
            <div class="shares flex flex-column align-center justify-center">
              <div class="name">
                <span>分享内容</span>
              </div>
              <div class="tool flex align-center">
                <div class="button cursor-pointer flex align-center" @click="shareToWechat">
                  <img src="@/assets/images/common/icon_wechat.png" alt="">
                  <span>微信</span>
                </div>
                <div class="button cursor-pointer flex align-center" @click="shareToWeibo(info.detail.title)">
                  <img src="@/assets/images/common/icon_sina.png" alt="">
                  <span>微博</span>
                </div>
                <div class="button cursor-pointer flex align-center" @click="shareToQQ(info.detail.title, '')">
                  <img src="@/assets/images/common/icon_tencent.png" alt="">
                  <span>QQ</span>
                </div>
              </div>
            </div>
          </div>
          <div class="copyright">
            <span>版权及免责声明：本网站所有文章除标明原创外，均来自网络。登载本文的目的为传播行业信息，内容仅供参考，如有侵权请联系删除。文章版权归原作者及原出处所有。本网拥有对此声明的最终解释权</span>
          </div>
          <div class="pages flex align-center justify-between">
            <div class="page-card bg-white flex-1" v-if="info.prevNext.prev">
              <div class="name">
                <span>{{info.prevNext.prev.title}}</span>
              </div>
              <el-button class="text-white" @click="goToArticle(info.prevNext.prev.id)">上一篇</el-button>
            </div>
            <div class="page-card bg-white flex-1 disabled" v-else>
              <div class="name">
                <span>没有上一篇</span>
              </div>
              <el-button class="text-white" disabled>上一篇</el-button>
            </div>
            <div class="page-card bg-white flex-1" v-if="info.prevNext.next">
              <div class="name">
                <span>{{info.prevNext.next.title}}</span>
              </div>
              <el-button class="text-white" @click="goToArticle(info.prevNext.next.id)">下一篇</el-button>
            </div>
            <div class="page-card bg-white flex-1 disabled" v-else>
              <div class="name">
                <span>没有下一篇</span>
              </div>
              <el-button class="text-white" disabled>下一篇</el-button>
            </div>
          </div>
        </div>
        <div class="slider">
          <div class="card">
            <otherCard title="热门标签" :icon="getAssetsFile('common/icon_tag.png')">
              <div class="tags">
                <el-link underline="never" class="tag cursor-pointer" v-for="tag in info.hotTags" :key="tag.name">
                  #{{tag.name}}
                </el-link>
              </div>
            </otherCard>
          </div>
          <div class="card">
            <otherCard title="最新文章" :icon="getAssetsFile('common/icon_book.png')">
              <div class="books">
                <div class="book cursor-pointer row1" v-for="article in info.latestArticles" :key="article.id" @click="goToArticle(article.id)">
                  <span>{{article.title}}</span>
                </div>
              </div>
            </otherCard>
          </div>
          <div class="card">
            <otherCard title="热点资讯" :icon="getAssetsFile('common/icon_hot.png')">
              <div class="books">
                <div class="book cursor-pointer row1" v-for="article in info.hotArticles" :key="article.id" @click="goToArticle(article.id)">
                  <span>{{article.title}}</span>
                </div>
              </div>
            </otherCard>
          </div>
          <div class="card">
            <adCard />
          </div>
        </div>
      </div>
    </div>
    <Footer />
  </div>
  <el-dialog
      v-model="dialogVisible"
      width="300"
      title="请使用微信扫码分享给好友"
      class="sharePop"
  >
    <div class="image flex align-center justify-center">
      <el-image :src="qrCode" fit="scale-down"></el-image>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .contents{
    margin: px2rem(24) 0 px2rem(100);
    .container{
      min-width: px2rem(1000);
      max-width: px2rem(1440);
      .info{
        margin-right: px2rem(20);
        min-width: 0;
        flex: 1;
        overflow: hidden;
        .content{
          padding: px2rem(34) px2rem(24);
          box-sizing: border-box;
          box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
          border-radius: px2rem(16);
          overflow-wrap: break-word;
          overflow-x: hidden;
          .title{
            color: #191B1F;
            font-size: px2rem(28);
            font-weight: 600;
            word-wrap: break-word;
            word-break: break-word;
            line-height: 1.4;
          }
          .html{
            word-wrap: break-word;
            word-break: break-word;
            line-height: 1.6;
            overflow: hidden;
            // 确保所有内容都不会溢出
            * {
              max-width: 100% !important;
              box-sizing: border-box;
            }
            img{
              max-width: 100% !important;
              height: auto !important;
              width: auto !important;
              display: block;
              margin: px2rem(10) 0;
            }
            table{
              max-width: 100%;
              overflow-x: auto;
              display: block;
              white-space: nowrap;
            }
            iframe, video, embed, object{
              max-width: 100% !important;
              height: auto !important;
            }
            // 深度选择器处理所有可能的媒体元素
            :deep(img){
              max-width: 100% !important;
              height: auto !important;
              width: auto !important;
            }
            :deep(table){
              max-width: 100%;
              overflow-x: auto;
              display: block;
              white-space: nowrap;
            }
            :deep(iframe), :deep(video), :deep(embed), :deep(object){
              max-width: 100% !important;
              height: auto !important;
            }
          }
          .tips{
            margin: px2rem(14) 0 px2rem(19);
            .author{
              color: #3670EE;
              font-size: px2rem(12);
              font-weight: 500;
              background-color: #EBF1FD;
              padding: px2rem(5) px2rem(10);
              border-radius: px2rem(4);
            }
            .time{
              color: #191B1F;
              font-size: px2rem(14);
              margin: 0 px2rem(14);
            }
            .zan{
              color: #FF6E1B;
              font-size: px2rem(14);
              img{
                width: px2rem(24);
                margin-right: px2rem(6);
              }
            }
          }
          .tools{
            margin: px2rem(50) 0 px2rem(60);
            .el-button{
              font-weight: 600;
              font-size: px2rem(20);
              background-color: #9DA5AE;
              width: px2rem(190);
              height: px2rem(70);
              min-height: unset;
              line-height: 1;
              padding: 0;
              border-radius: px2rem(8);
              border: 0;
              &:hover{
                color: #fff;
              }
              &.active{
                background: linear-gradient( 180deg, #39B3E6 0%, #3B78C8 100%);
              }
              img{
                width: px2rem(24);
                height: px2rem(24);
                margin-right: px2rem(10);
              }
            }
          }
          .shares{
            .name{
              font-size:  px2rem(20);
            }
            .tool{
              margin-top: px2rem(20);
              .button{
                color: #8A8A8A;
                font-size: px2rem(16);
                padding: px2rem(10) px2rem(24);
                box-sizing: border-box;
                background-color: #F3F5F7;
                border-radius: px2rem(8);
                &+.button{
                  margin-left: px2rem(8);
                }
                img{
                  width: px2rem(22);
                  height: px2rem(22);
                  margin-right: px2rem(6);
                }
              }
            }
          }
        }
        .copyright{
          margin: px2rem(30) 0 px2rem(50);
          color: #8A8A8A;
          font-size: px2rem(14);
          word-wrap: break-word;
          word-break: break-word;
        }
        .pages{
          .page-card{
            padding: px2rem(24);
            box-sizing: border-box;
            color: #191B1F;
            font-size: px2rem(22);
            font-weight: 500;
            box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
            border-radius: px2rem(16);
            &+.page-card{
              margin-left: px2rem(31);
            }
            &.disabled{
              .name{
                color: #999999;
              }
            }
            .name{
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              line-height: 1.4;
              max-height: 2.8em;
              margin-bottom: px2rem(10);
              word-wrap: break-word;
              word-break: break-word;
            }
            .el-button{
              background: linear-gradient( 180deg, #39B3E6 0%, #3B78C8 100%);
              border-radius: px2rem(8);
              width: 100%;
              border: 0;
              margin-top: px2rem(40);
              height: px2rem(54);
              &:hover{
                color: #fff;
              }
              &:disabled{
                background: #DDDDDD;
                color: #999999;
                cursor: not-allowed;
              }
            }
          }
        }
      }
      .slider{
        width: px2rem(353);
        flex-shrink: 0;
        .card {
          & + .card {
            margin-top: px2rem(20);
          }
          .tags{
            //flex-wrap: wrap;
            &::before{
              content: ' ';
              display: table;
            }
            &::after{
              clear: both;
              content: ' ';
              display: table;
            }
            .tag{
              display: inline-block;
              background-color: #E5ECFE;
              color: $maincolor;
              font-size: px2rem(14);
              padding: px2rem(5) px2rem(10);
              border-radius: px2rem(19);
              margin-right: px2rem(10);
              margin-bottom: px2rem(16);
            }
          }
          .books{
            .book{
              background-color: #F9F9FA;
              border-radius: px2rem(8);
              padding: 0 px2rem(12);
              box-sizing: border-box;
              color: #191B1F;
              font-size: px2rem(16);
              height: px2rem(42);
              line-height: px2rem(42);
              &+.book{
                margin-top: px2rem(10);
              }
            }
          }
        }
      }
    }
  }
}
</style>