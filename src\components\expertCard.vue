<script setup lang="ts">
import { safePush } from "@/utils/router.ts";
import {isHttpOrHttps} from "@/utils/utils.ts";
const { info, isFlex, avatarSize } = defineProps({
  info: {
    type: Object,
    required: true,
  },
  isFlex: {
    type: Boolean,
    required: false,
    default: false,
  },
  avatarSize: {
    type: Number,
    required: false,
    default: 94
  }
});
</script>

<template>
  <div class="item hidden cursor-pointer bg-white" :class="{flex: isFlex}" @click="safePush({path:'/expert/details',query:{id:info.id}})">
    <div class="avatar flex hidden" :style="{width: avatarSize+'px', height: avatarSize+'px'}">
      <el-avatar :size="94" :src="isHttpOrHttps(info.avatar_url)" fit="cover" />
    </div>
    <div class="info flex-1" :style="{marginLeft: isFlex ? '16px' : 0, marginTop: isFlex ? 0 : '16px'}">
      <div class="names flex align-center">
        <div class="name">
          <span v-html="info.title"></span>
        </div>
        <div class="tag">
          <span>{{info.expert_number ?? '-'}}</span>
        </div>
      </div>
      <div class="job">
        <span>{{info.summary}}</span>
      </div>
      <div class="tags">
        <div class="tag" v-for="(item, index) in info.research_area?.split(',')" :key="index">
          <span>{{item}}</span>
        </div>
      </div>
      <div class="description row2">
        <span>{{info.academic_achievements}}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item{
  box-shadow: 0 px2rem(2) px2rem(21) 0 rgba(0, 66, 190, 0.14);
  border-radius: px2rem(24);
  padding: px2rem(20) px2rem(16) px2rem(16);
  box-sizing: border-box;
  width: 100%;

  .avatar {
    border-radius: 50%;
    margin: 0 auto;
    img{
      width: 100%;
      height: 100%;
    }
  }

  .info{
    .names {
      .name {
        font-size: px2rem(22);
        font-weight: 500;
      }

      .tag {
        padding: px2rem(5) px2rem(10);
        box-sizing: border-box;
        background: linear-gradient(90deg, #F8F0DF 0%, #EFE3C8 100%);
        border-radius: px2rem(6);
        margin-left: px2rem(10);
        color: #452111;
        font-weight: 500;
        font-size: px2rem(14);
      }
    }

    .job {
      color: #535871;
      font-size: px2rem(16);
      font-weight: 600;
      margin: px2rem(9) 0 px2rem(8);
    }

    .tags {
      display: flex;
      flex-wrap: wrap;

      .tag {
        padding: px2rem(4) px2rem(8);
        box-sizing: border-box;
        border-radius: px2rem(4);
        border: px2rem(1) solid #CCCCCC;
        font-size: px2rem(13);
        color: #434864;
        margin-right: px2rem(8);
        margin-bottom: px2rem(8);
      }
    }

    .description {
      margin-top: px2rem(2);
      color: #535871;
      font-size: px2rem(16);
      line-height: px2rem(22);
    }
  }

}
</style>