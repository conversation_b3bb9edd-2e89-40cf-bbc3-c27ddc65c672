<script setup lang="ts">
import { useSystemStore } from '@/store/system.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
const systemStore = useSystemStore()
</script>

<template>
  <div class="ad bg-white flex justify-between align-center">
    <div class="_left">
      <div class="title">
        <span>学术资源免费领取</span>
      </div>
      <div class="line"></div>
      <div class="desc">
        <span>加微信领取20G科研大礼包！更多众多热门</span>
      </div>
    </div>
    <div class="_right">
      <img :src="isHttpOrHttps(systemStore.kefu_qrcode)" alt="">
    </div>
  </div>
</template>

<style scoped lang="scss">
.ad{
  width: 100%;
  padding: 12px;
  box-sizing:  border-box;
  box-shadow: 0 1px 3px 0 rgba(8,48,123,0.06);
  border-radius: 12px;
  ._left{
    width: 175px;
    .title{
      color: #EA7F46;
      font-size: 16px;
      line-height: 1;
    }
    .line{
      background-color: #EA7F46;
      margin: 5px 0 22px;
      width: 22px;
      height: 2px;
    }
    .desc{
      font-size: 13px;
      color: #9098A1;
      line-height: 20px;
    }
  }
  ._right{
    width: 88px;
    height: 88px;
    img{
      width: 100%;
    }
  }
}
</style>