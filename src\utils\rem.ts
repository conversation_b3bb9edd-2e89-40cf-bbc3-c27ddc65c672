export const initRem = () => {
    const designWidth = 1920
    const designHeight = 1080
    const baseSize = 16
    const maxScale = 2 // 最大缩放比例
    const minScale = 0.5 // 最小缩放比例

    let scale = Math.min(
        window.innerWidth / designWidth,
        window.innerHeight / designHeight
    )

    // 限制缩放范围
    scale = Math.max(minScale, Math.min(scale, maxScale))

    // 设置CSS变量
    document.documentElement.style.setProperty('--scale', scale.toString())
    document.documentElement.style.setProperty('--base-size', baseSize.toString())
    document.documentElement.style.fontSize = `${baseSize * scale}px`
    document.body.style.fontSize = '1rem'

    console.log(`当前缩放比例: ${scale}, font-size: ${baseSize * scale}px`)

    return {
        baseSize,
        scale
    }
}

declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $px2rem: (px: number) => string;
    }
}

const install = (Vue:any) => {
    initRem()
    Vue.config.globalProperties.$px2rem = (px:number) => `calc(${px} / var(--base-size) * 1rem)`
}

export default {
    install
}
