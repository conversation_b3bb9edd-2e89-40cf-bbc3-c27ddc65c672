<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from "vue-router";
import {getAssetsFile} from "@/utils/utils.ts";
import {safePush} from "@/utils/router.ts";
const route = useRoute();
const footerRef = ref()
const menus = ref([
  {label: '主页', icon: getAssetsFile('common_mobile/icon_home.png'), iconActive: getAssetsFile('common_mobile/icon_home_active.png'), url: '/home'},
  {label: '学术服务', icon: getAssetsFile('common_mobile/icon_service.png'), iconActive: getAssetsFile('common_mobile/icon_service_active.png'), url: '/service'},
  {label: '专家智库', icon: getAssetsFile('common_mobile/icon_expert.png'), iconActive: getAssetsFile('common_mobile/icon_expert_active.png'), url: '/expert'},
  {label: '学术期刊', icon: getAssetsFile('common_mobile/icon_journal.png'), iconActive: getAssetsFile('common_mobile/icon_journal_active.png'), url: '/journal'},
  {label: '我的', icon: getAssetsFile('common_mobile/icon_my.png'), iconActive: getAssetsFile('common_mobile/icon_my_active.png'), url: '/user'},
])
const getFooterHeight = computed(() => {
  return footerRef.value ? footerRef.value.clientHeight : 0
})
</script>

<template>
  <div class="block" :style="{height: getFooterHeight + 'px'}"></div>
  <footer ref="footerRef" class="bg-white position-fixed">
    <div class="menus flex align-center justify-between">
      <div class="menu flex flex-column align-center" :class="{active: route.path.includes(item.url)}" v-for="(item, index) in menus" :key="index" @click="safePush(item.url)">
        <div class="icon">
          <el-image :src="route.path.includes(item.url) ? item.iconActive : item.icon"></el-image>
        </div>
        <div class="name">{{item.label}}</div>
      </div>
    </div>
  </footer>
</template>

<style scoped lang="scss">
.block{
  width: 100%;
}
footer{
  box-sizing: border-box;
  padding-left: 28.5px;
  padding-right: 28.5px;
  padding-top: 11.5px;
  padding-bottom: 11.5px;
  padding-bottom: calc(11.5px + constant(safe-area-inset-bottom));
  padding-bottom: calc(11.5px + env(safe-area-inset-bottom));
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 9;
  box-shadow: 0 0 6px 0 rgba(175,175,183,0.35);
  .menus{
    .menu{
      color: #BFC0CD;
      &.active{
        color: #3B78C8;
      }
      .icon{
        width: 19.5px;
        height: 19.5px;
      }
      .name{
        margin-top: 4px;
        font-size: 10px;
        line-height: 1;
      }
    }
  }
}
</style>