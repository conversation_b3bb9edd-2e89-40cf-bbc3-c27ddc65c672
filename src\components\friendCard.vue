<script setup lang="ts">

import { useSystemStore } from '@/store/system.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
const systemStore = useSystemStore()
</script>

<template>
  <div class="friend-card bg-white flex flex-column align-center justify-center">
    <img :src="isHttpOrHttps(systemStore.kefu_qrcode)" alt="">
    <div class="tips">
      <span>立即添加好友，了解更多</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.friend-card{
  width: 100%;
  padding: px2rem(38) px2rem(0);
  box-sizing: border-box;
  box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
  border-radius: px2rem(16);
  img{
    width: px2rem(160);
    border-radius: px2rem(10);
  }
  .tips{
    margin-top: px2rem(24);
    color: #191B1F;
    font-size: px2rem(20);
  }
}
</style>