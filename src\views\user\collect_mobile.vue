<script setup lang="ts">
import {onMounted, ref, watch} from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import {backPage} from "@/utils/router.ts";
import serviceCardMobile from "@/components/serviceCardMobile.vue";
import expertCardMobile from "@/components/expertCardMobile.vue";
import journalCardMobile from '@/components/journalCardMobile.vue';
import storiesCardMobile from "@/components/storiesCardMobile.vue";
import paginateMobile from "@/components/paginateMobile.vue";
import { myCollects } from '@/api/common.ts'
const tabs = ref([
  {name: '全部', value: '', row: 3},
  {name: '学术科研服务', value: 'SERVICE', row: 3},
  {name: '专家智库', value: 'EXPERT', row: 3},
  {name: '学术期刊', value: 'JOURNAL', row: 3},
  {name: '学术头条', value: 'HEADLINE', row: 2},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref([])
const total = ref(0)
const getMyCollects = () => {
  myCollects({
    content_type: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value
  }).then(res=>{
    total.value = res.data.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getMyCollects()
}
watch(tabCur, (val) => {
  page.value = 1
  list.value = []
  getMyCollects()
},{
  deep: true
})
onMounted(()=>{
  getMyCollects()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 flex flex-column hidden">
      <div class="tabs bg-white flex align-center justify-between">
        <div class="tab" :class="{active: tabCur === index}" v-for="(item, index) in tabs" :key="index" @click="tabCur = index">
          <span>{{item.name}}</span>
        </div>
      </div>
      <div class="list flex-1 flex flex-column hidden">
        <div class="items flex-1 auto">
          <div class="part" v-for="(item, index) in list" :key="index">
            <template v-if="item.content.content_type === 'SERVICE'">
              <serviceCardMobile :info="item.content" />
            </template>
            <template v-else-if="item.content.content_type === 'EXPERT'">
              <expertCardMobile :info="item.content" />
            </template>
            <template v-else-if="item.content.content_type === 'JOURNAL'">
              <journalCardMobile :info="item.content" />
            </template>
            <template v-else-if="item.content.content_type === 'HEADLINE'">
              <storiesCardMobile :info="item.content" />
            </template>
          </div>
        </div>
        <div class="paginates">
          <paginateMobile :limit="limit" :total="total" @change="changePage" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: url("@/assets/images/common_mobile/bg_user.png") no-repeat;
  background-size: cover;

  .box {

    .tabs{
      font-size: 13px;
      margin-top: 7px;
      padding: 13px 15px 9px;
      box-sizing: border-box;
      .tab{
        border-bottom: 2px solid transparent;
        &.active{
          color: $maincolor;
          border-color: $maincolor;
        }
      }
    }
    .list{
      padding: 12px 10px;
      box-sizing: border-box;
      .items{
        display: grid;
        grid-template-columns:repeat(2, 1fr);
        grid-gap: 12px 11px;
        grid-auto-rows: min-content;
      }
      .paginates{
        margin-top: 10px;
      }
    }
  }
}
</style>