<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue'
import HeaderMobile from "@/components/headerMobile.vue";
import ShareCardMobile from "@/components/shareCardMobile.vue";
import OtherCardMobile from "@/components/otherCardMobile.vue";
import ServiceCardMobile from "@/components/serviceCardMobile.vue";
import AboutCardMobile from "@/components/aboutCardMobile.vue";
import ExpertCardMobile from "@/components/expertCardMobile.vue";
import JournalCardMobile from "@/components/journalCardMobile.vue";
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import {Close, Star, StarFilled} from '@element-plus/icons-vue'
import {contentDetail, toggleCollect, createOrderAndPayWap, checkPayStatus, createConsultation} from '@/api/common.ts'
import {ElMessage, type FormInstance} from "element-plus";
import { useRoute, useRouter } from 'vue-router'
import { useSystemStore } from '@/store/system'
import HeaderTitle from "@/components/headerTitle.vue";
import dayjs from "dayjs";
const systemStore = useSystemStore()
const route = useRoute();
const router = useRouter();
let timeObj: any = null
const orderInfo = ref({})
const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: []
})
const payCur = ref('alipay')
const dialogFormVisible = ref(false)
const ruleFormRef = ref<FormInstance>()
const consultationFormRef = ref<FormInstance>()

const buyForm = ref({
  name: '',
  phone: '',
  email: '',
  type: 'alipay'
})

// 咨询表单数据
const consultationForm = ref({
  contact_name: '',
  contact_mobile: '',
  contact_email: '',
  specialization: '',
  content: ''
})

const rules = ref({
  name: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  email: [
    {
      pattern: /^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ]
})

// 咨询表单验证规则
const consultationRules = ref({
  contact_name: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contact_mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  contact_email: [
    {
      pattern: /^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ],
  content: [
    { required: true, message: '请输入咨询内容', trigger: 'blur' },
    { min: 10, message: '咨询内容至少10个字符', trigger: 'blur' }
  ]
})

const dialogStudyVisible = ref(false)
const dialogConsultationVisible = ref(false)
const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
    ElMessage.success(res.msg)
  })
}
const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
const handleOperate = () => {
  if ((info.value.detail as any).price > 0) {
    dialogFormVisible.value = true
    ruleFormRef.value?.resetFields()
  } else {
    // 免费咨询，打开咨询弹窗
    dialogConsultationVisible.value = true
    consultationFormRef.value?.resetFields()
  }
}

// 提交咨询表单
const submitConsultation = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      dialogConsultationVisible.value = false
      createConsultation({
        service_id: (info.value.detail as any).id,
        contact_name: consultationForm.value.contact_name,
        contact_mobile: consultationForm.value.contact_mobile,
        contact_email: consultationForm.value.contact_email,
        specialization: consultationForm.value.specialization,
        content: consultationForm.value.content
      }).then(res => {
        ElMessage.success('您的咨询已受到，我们会尽快回复。')
        // 跳转到用户中心的咨询列表
        router.push('/user/consultation')
      }).catch(err => {
        ElMessage.error('提交失败，请重试')
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      dialogFormVisible.value = false
      createOrderAndPayWap({
        service_id: info.value.detail.id,
        contact_name: buyForm.value.name,
        contact_mobile: buyForm.value.phone,
        pay_type: payCur.value,
        contact_email: buyForm.value.email,
      }).then(res => {
        orderInfo.value = res.data
        if (payCur.value === 'wechat') {
          window.location.href = res.data.qr_code
        } else if (payCur.value === 'alipay') {
          submitAlipayForm(res.data.pay_url);
        }
        timeObj = setInterval(() => {
          checkOrders()
        }, 3000)
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const submitAlipayForm = (formData: string) => {
  console.log('formData', formData)
  const div = document.createElement('div');
  div.innerHTML = formData;
  document.body.appendChild(div);
  const form = div.querySelector('form') as HTMLFormElement;
  form.submit();
}
const checkOrders = () => {
  checkPayStatus({order_id: orderInfo.value.order_id}).then(res => {
    if (['paid', 'completed'].includes(res.data.status)) {
      // 已付款、已完成
      clearInterval(timeObj)
      ElMessage.success(res.msg)
      getDetails()
    } else if (['cancelled', 'refunded'].includes(res.data.status)) {
      // 已取消、已退款
      clearInterval(timeObj)
      ElMessage.error(res.msg)
      getDetails()
    }
  })
}
const handleStudy = () => {
  dialogStudyVisible.value = true
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})
onUnmounted(()=>{
  timeObj && clearInterval(timeObj)
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="flex-1 auto" style="overflow-x: hidden">
      <div class="thumb">
        <img class="flex" :src="isHttpOrHttps(info.detail.cover_image)" alt="">
      </div>
      <div class="info bg-white">
        <div class="title">
          <span>{{info.detail.title}}</span>
        </div>
        <div class="desc row4">
          <span>{{info.detail.subtitle}}</span>
        </div>
        <div class="prices flex justify-between align-end" v-if="info.detail.price > 0">
          <div class="money">
            <span>¥</span>
            <span>{{info.detail.price}}</span>
          </div>
          <div class="nums">
            <span>{{info.detail.purchase_count}}人购买</span>
          </div>
        </div>
        <div style="margin-top: 18px">
          <shareCardMobile :title="info.detail.title" :image="isHttpOrHttps(info.detail.cover_image)">
            <div class="tool">
              <template v-if="info.detail.price > 0">
                <template v-if="info.detail.has_purchased">
                  <el-button type="success" @click="handleStudy">立即学习</el-button>
                </template>
                <template v-else>
                  <el-button type="primary" @click="handleOperate">购买</el-button>
                </template>
              </template>
              <template v-else>
                <el-button type="primary" @click="handleOperate">免费咨询</el-button>
              </template>
              <el-button color="#EEAF3D" :icon="info.detail?.is_collected ? StarFilled : Star" @click="handleCollect">收藏</el-button>
            </div>
          </shareCardMobile>
        </div>
        <div class="card">
          <div class="part flex">
            <div class="name">
              <span>服务保障</span>
            </div>
            <div class="tips flex align-center">
              <span>一对一服务 · 可加急 · 无忧售后 · 提供发票 · 极速退款</span>
            </div>
          </div>
          <div class="part flex align-center">
            <div class="name">
              <span>服务保障</span>
            </div>
            <div class="tips flex align-center">
              <img src="@/assets/images/common/icon_wxpay.png" alt="">
              <img src="@/assets/images/common/icon_alipay.png" alt="">
              <img src="@/assets/images/common/icon_bankcard.png" alt="">
            </div>
          </div>
        </div>
        <div class="content">
          <div class="name-bar flex align-center justify-center">
            <img src="@/assets/images/home/<USER>" alt="">
            <span>服务详情</span>
            <img src="@/assets/images/home/<USER>" alt="">
          </div>
          <div class="html" v-html="info.detail.service_desc"></div>
        </div>
      </div>
      <div class="card bg-white" v-if="info.reviews?.length">
        <OtherCardMobile title="真实用户评价" :icon="getAssetsFile('common/icon_edit.png')">
          <div class="evaluates">
            <div class="evaluate" v-for="(item, index) in info.reviews" :key="index">
              <div class="title flex align-center justify-between">
                <div class="_left">
                  <span>{{item.user?.nickname}}</span>
                </div>
                <div class="_right">
                  <span>{{dayjs(item.create_time*1000).format('YY年MM月DD日')}}</span>
                </div>
              </div>
              <div class="stars">
                <el-icon v-for="i in item.rating" size="16">
                  <StarFilled color="#EEAF3D" />
                </el-icon>
              </div>
              <div class="say" v-html="item.content"></div>
            </div>
          </div>
        </OtherCardMobile>
      </div>
      <div class="card">
        <OtherCardMobile title="相关推荐" :icon="getAssetsFile('common/icon_zan.png')" more-url="/service">
          <div class="services">
            <serviceCardMobile :info="item" v-for="(item, index) in info.recommended" :key="index" />
          </div>
          <div class="about" v-if="info.related.experts?.length">
            <aboutCardMobile title="相关专家" :icon="getAssetsFile('common/icon_zj.png')" moreUrl="/expert">
              <div class="items">
                <ExpertCardMobile :info="item" v-for="(item, index) in info.related.experts" :key="index" />
              </div>
            </aboutCardMobile>
          </div>
          <div class="about" v-if="info.related.journals?.length">
            <aboutCardMobile title="相关书籍" :icon="getAssetsFile('common/icon_books.png')" moreUrl="/journal">
              <div class="items">
                <JournalCardMobile :info="item" v-for="(item, index) in info.related.journals" :key="index" />
              </div>
            </aboutCardMobile>
          </div>
        </OtherCardMobile>
      </div>
    </div>
  </div>
  <el-drawer
      v-model="dialogFormVisible"
      :show-close="false"
      size="100%"
      class="mobile-buy-pop"
      direction="btt"
  >
    <template #header="{ close }">
      <HeaderTitle @click="close"></HeaderTitle>
    </template>
    <div class="buy-info">
      <div class="pay-box text-center bg-white">
        <div class="title">
          <span>在线快速支付</span>
        </div>
        <div class="price">
          <span>¥</span>
          <span>{{info.detail.price}}</span>
        </div>
        <div class="unit">
          <span>订单金额</span>
        </div>
      </div>
      <div class="pay-box text-center bg-white">
        <el-form ref="ruleFormRef" :model="buyForm" :rules="rules" label-position="left" label-width="auto">
          <el-form-item label="联系人" prop="name">
            <el-input v-model="buyForm.name" placeholder="请输入联系人" />
          </el-form-item>
          <el-form-item label="电话" prop="phone">
            <el-input v-model="buyForm.phone" placeholder="请输入电话" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="buyForm.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-form>
      </div>
      <div class="pay-box text-center bg-white">
        <div class="pays">
          <div class="pay flex justify-between align-center" :class="{active: payCur === 'alipay'}" @click="payCur='alipay'">
            <div class="left flex align-center">
              <el-image :src="getAssetsFile('common/icon_alipay_pay.png')"></el-image>
              <span>支付宝</span>
            </div>
            <div class="right" v-if="payCur === 'alipay'">
              <img class="flex" src="@/assets/images/common_mobile/icon_checked.png" alt="">
            </div>
          </div>
          <div class="pay flex justify-between align-center" :class="{active: payCur === 'wechat'}" @click="payCur='wechat'">
            <div class="left flex align-center">
              <el-image :src="getAssetsFile('common/icon_wechat_pay.png')"></el-image>
              <span>微信</span>
            </div>
            <div class="right" v-if="payCur === 'wechat'">
              <img class="flex" src="@/assets/images/common_mobile/icon_checked.png" alt="">
            </div>
          </div>
        </div>
      </div>
      <div class="buttons">
        <el-button type="primary" @click="submit(ruleFormRef)">确认支付</el-button>
      </div>
    </div>
  </el-drawer>

  <!-- 咨询弹窗 -->
  <el-dialog
      v-model="dialogConsultationVisible"
      class="consultationMobilePop"
      :show-close="false"
  >
    <div class="consultation-form">
      <div class="title">免费咨询</div>
      <el-form ref="consultationFormRef" :model="consultationForm" :rules="consultationRules" label-position="top">
        <el-form-item label="联系人" prop="contact_name">
          <el-input v-model="consultationForm.contact_name" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="手机号" prop="contact_mobile">
          <el-input v-model="consultationForm.contact_mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="consultationForm.contact_email" placeholder="请输入邮箱（选填）" />
        </el-form-item>
        <el-form-item label="专业方向">
          <el-input v-model="consultationForm.specialization" placeholder="请输入专业方向（选填）" />
        </el-form-item>
        <el-form-item label="咨询内容" prop="content">
          <el-input
            v-model="consultationForm.content"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您的咨询问题，至少10个字符"
          />
        </el-form-item>
      </el-form>
      <div class="buttons">
        <el-button type="primary" @click="submitConsultation(consultationFormRef)">提交咨询</el-button>
        <el-button @click="dialogConsultationVisible = false">取消</el-button>
      </div>
    </div>
  </el-dialog>

  <el-dialog
      v-model="dialogStudyVisible"
      class="studyMobilePop"
      :show-close="false"
  >
    <div class="study-info">
      <div class="title flex align-center justify-center">
        <img src="@/assets/images/common/icon_study_left.png" alt="">
        <div class="name">立即学习</div>
        <img src="@/assets/images/common/icon_study_right.png" alt="">
      </div>
      <div class="code">
        <img :src="isHttpOrHttps(systemStore.kefu_qrcode)" alt="">
      </div>
      <div class="tips flex justify-center">
        <span>扫描二维码联系助理老师进行学习</span>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.page {
  background: #F4F6F9;
  height: 100vh;
  .thumb{
    img{
      width: 100%;
    }
  }
  .info{
    padding: 16px 15px 40px;
    box-sizing: border-box;
    .title{
      color: #1F1F1F;
      font-size: 18px;
      font-weight: 600;
    }
    .desc{
      color: #5C6671;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
    }
    .prices{
      margin-top: 5px;
      .money{
        color: #E64F26;
        font-weight: 500;
        span{
          &:first-child{
            font-size: 12px;
            padding-right: 2px;
          }
          &:last-child{
            font-size: 18px;
          }
        }
      }
      .nums{
        font-size: 13px;
        color: #696A77;
        font-weight: 400;
      }
    }
    .tool{
      .el-button{
        width: 98px;
        height: 32px;
        font-size: 14px;
        border-radius: 27px;
        &:first-child{

        }
        &:last-child{
          background-color: transparent;
          color: #EEAF3D;
        }
      }
    }
    .card{
      margin-top: 12px;
      background-color: #F4F6F9;
      border-radius: 8px;
      padding: 16px 12px;
      box-sizing: border-box;
      .part{
        &+.part{
          margin-top: 17px;
        }
        .name{
          color: #5C6671;
          font-size: 12px;
          width: 55px;
        }
        .tips{
          margin-left: 10px;
          color: #191B1F;
          font-size: 12px;
          img{
            width: 22px;
            height: 22px;
            &+img{
              margin-left: 12px;
            }
          }
        }
      }
    }
    .content{
      .name-bar{
        color: $maincolor;
        font-weight: 600;
        font-size: 14px;
        margin: 30px 0;
        span{
          padding: 0 px2rem(30);
          box-sizing: border-box;
        }
        img{
          width: 40px;
        }
      }
      .html{
        img{
          width: 100%;
        }
      }
    }
  }
  .card{
    margin-top: 5px;
    .evaluates{
      .evaluate{
        .title{
          font-size: 14px;
          line-height: 1;
          ._left{
            color: #191B1F;
          }
          ._right{
            color: #909090;
            font-size: 12px;
          }
        }
        .stars{
          margin: 8px 0 12px;
        }
        .say{
          color: #1F1F1F;
          font-size: 12px;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
    .services{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px 11px;
      grid-auto-rows: min-content;
    }
    .about{
      margin-top: 30px;
      .items{
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 12px 11px;
        grid-auto-rows: min-content;
      }
    }
  }
}
.study-info{
  .title{
    .name{
      font-size: 18px;
      color: #36B96B;
      font-weight: 600;
      margin: 0 16px;
    }
    img{
      width: 37px;
    }
  }
  .code{
    width: 119px;
    margin: 44px auto 45px;
    img{
      width: 100%;
    }
  }
  .tips{
    color: #36B96B;
    font-weight: 600;
    font-size: 18px;
  }
}
</style>