<template>
  <div class="paginate flex justify-center align-center">
    <el-pagination :default-page-size="limit" hide-on-single-page background layout="prev, pager, next, total" size="small" :total="total" @change="change" />
  </div>
</template>

<script setup lang="ts">
const { total } = defineProps({
  total: {
    type: Number,
    required: true,
  },
  limit: {
    type: Number,
    required: false,
    default: 10,
  }
})

const emits = defineEmits(["change"]);

const change = (e: number) => {
  emits('change', e);
}
</script>

<style scoped lang="scss">
.paginate{
  margin-right: px2rem(30);
  .total{
    font-size: px2rem(16);
    margin-right: px2rem(16);
  }
  :deep(.el-pagination){
    .el-pager{
      .number{
        font-size: px2rem(16);
        background-color: $whitecolor;
        color: #59617F;
        width: px2rem(34);
        height: px2rem(34);
        padding: 0;
        box-sizing: border-box;
        &.is-active{
          color: #fff;
          background-color: $maincolor;
        }
      }
    }
    .btn-prev{
      background-color: $whitecolor;
      color: #59617F;
      width: px2rem(34);
      height: px2rem(34);
      padding: 0;
      box-sizing: border-box;
    }
    .btn-next{
      background-color: $whitecolor;
      color: #59617F;
      width: px2rem(34);
      height: px2rem(34);
      padding: 0;
      box-sizing: border-box;
    }
    .el-pagination__total{
      color: #8E94A1;
      font-size: px2rem(14);
    }
  }

}
</style>
