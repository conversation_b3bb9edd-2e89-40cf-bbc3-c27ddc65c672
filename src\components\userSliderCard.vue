<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from "vue-router";
import {safePush} from "@/utils/router.ts";
const route = useRoute();
const menus = ref([
  {name: '我的订单', url: '/user/index'},
  {name: '我的咨询', url: '/user/consultation'},
  {name: '我的收藏', url: '/user/collect'},
  {name: '我的资料', url: '/user/account'},
  {name: '账号设置', url: '/user/set'},
])
</script>

<template>
<div class="slider bg-white">
  <div class="menus">
    <div class="menu cursor-pointer position-relative" :class="{active: route.path.includes(item.url)}" v-for="(item, index) in menus" @click="safePush(item.url)">
      <span>{{item.name}}</span>
    </div>
  </div>
</div>
</template>

<style scoped lang="scss">
.slider{
  width: 100%;
  height: px2rem(600);
  border-radius: px2rem(16);
  .menus{
    padding: px2rem(22) px2rem(20);
    box-sizing:  border-box;
    .menu{
      padding: px2rem(10) px2rem(20);
      box-sizing:  border-box;
      border-radius: px2rem(8);
      color: #535871;
      font-size: px2rem(20);
      &+.menu{
        margin-top: px2rem(20);
      }
      &.active{
        background-color: $maincolor;
        color: #fff;
        &::before{
          width: px2rem(2);
          height: 100%;
          background-color: $maincolor;
          content: '';
          position: absolute;
          top: 0;
          left: px2rem(-20);
        }
      }
    }
  }
}
</style>