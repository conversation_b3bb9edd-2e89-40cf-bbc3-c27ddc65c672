<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue'
import { Star, StarFilled } from '@element-plus/icons-vue'
import Pay from "@/components/pay.vue";
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import ExpertCard from "@/components/expertCard.vue";
import JournalCard from "@/components/journalCard.vue";
import friendCard from "@/components/friendCard.vue";
import aboutCard from "@/components/aboutCard.vue";
import otherCard from "@/components/otherCard.vue";
import serviceCard from "@/components/serviceCard.vue";
import ShareCard from "@/components/shareCard.vue";
import { useSystemStore } from '@/store/system.ts'
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import { contentDetail, toggleCollect, createOrderAndPay, checkPayStatus, createConsultation, getCaptcha } from '@/api/common.ts'
import { useRoute, useRouter } from 'vue-router'
import {ElMessage, type FormInstance} from 'element-plus'
import dayjs from "dayjs";
const route = useRoute();
const router = useRouter();
const systemStore = useSystemStore()
let timeObj: any = null
const payRef = ref()
const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: []
})
const orderInfo = ref({})
const ruleFormRef = ref<FormInstance>()
const consultationFormRef = ref<FormInstance>()
const buyForm = ref({
  name: '',
  phone: '',
  email: '',
  type: 'alipay'
})

// 咨询表单数据
const consultationForm = ref({
  contact_name: '',
  contact_mobile: '',
  contact_email: '',
  specialization: '',
  captcha: ''
})

// 验证码相关
const captchaId = ref('')
const captchaUrl = ref('')

const rules = ref({
  name: [
      { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  email: [
    {
      pattern: /^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ]
})

// 咨询表单验证规则
const consultationRules = ref({
  contact_name: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contact_mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  contact_email: [
    {
      pattern: /^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度为4-6位', trigger: 'blur' }
  ]
})

const dialogFormVisible = ref(false)
const dialogStudyVisible = ref(false)
const dialogConsultationVisible = ref(false)
const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
      ElMessage.success(res.msg)
  })
}
const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
// 生成验证码ID
const generateCaptchaId = () => {
  return 'captcha_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 刷新验证码
const refreshCaptcha = async () => {
  captchaId.value = generateCaptchaId()
  console.log('刷新验证码ID:', captchaId.value)

  try {
    const response = await getCaptcha(captchaId.value)
    // 将blob转换为URL
    const blob = new Blob([response], { type: 'image/png' })
    captchaUrl.value = URL.createObjectURL(blob)
    console.log('验证码URL生成成功:', captchaUrl.value)
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请重试')
  }
}

const handleOperate = () => {
  if ((info.value.detail as any).price > 0) {
    dialogFormVisible.value = true
    ruleFormRef.value?.resetFields()
  } else {
    // 免费咨询，打开咨询弹窗
    dialogConsultationVisible.value = true
    consultationFormRef.value?.resetFields()
    // 生成验证码
    refreshCaptcha()
  }
}

// 提交咨询表单
const submitConsultation = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      dialogConsultationVisible.value = false
      createConsultation({
        service_id: (info.value.detail as any).id,
        contact_name: consultationForm.value.contact_name,
        contact_mobile: consultationForm.value.contact_mobile,
        contact_email: consultationForm.value.contact_email,
        specialization: consultationForm.value.specialization,
        captchaId: captchaId.value,
        captcha: consultationForm.value.captcha
      }).then(res => {
        ElMessage.success('您的咨询已受到，我们会尽快回复。')
        // 跳转到用户中心的咨询列表
        router.push('/user/consultation')
      }).catch(err => {
        ElMessage.error('提交失败，请重试')
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      dialogFormVisible.value = false
      createOrderAndPay({
        service_id: info.value.detail.id,
        contact_name: buyForm.value.name,
        contact_mobile: buyForm.value.phone,
        pay_type: buyForm.value.type,
        contact_email: buyForm.value.email,
      }).then(res => {
        orderInfo.value = res.data
        payRef.value.open()
        timeObj = setInterval(() => {
          checkOrders()
        }, 3000)
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const checkOrders = () => {
  checkPayStatus({order_id: orderInfo.value.order_id}).then(res => {
    if (['paid', 'completed'].includes(res.data.status)) {
      // 已付款、已完成
      payRef.value.close()
      clearInterval(timeObj)
      ElMessage.success(res.msg)
      getDetails()
    } else if (['cancelled', 'refunded'].includes(res.data.status)) {
      // 已取消、已退款
      payRef.value.close()
      clearInterval(timeObj)
      ElMessage.error(res.msg)
      getDetails()
    }
  })
}
const handleStudy = () => {
  dialogStudyVisible.value = true
}
const handleClosePop = () => {
  timeObj && clearInterval(timeObj)
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})
onUnmounted(()=>{
  timeObj && clearInterval(timeObj)
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="introduce">
      <div class="container">
        <div class="box bg-white flex justify-between">
          <div class="left hidden">
            <img :src="isHttpOrHttps(info.detail.cover_image)" alt="">
          </div>
          <div class="right flex-1">
            <div class="title">
              <span>{{info.detail.title}}</span>
            </div>
            <div class="desc row3">
              <span>{{info.detail.subtitle}}</span>
            </div>
            <div class="prices flex justify-between align-center" v-if="info.detail.price > 0">
              <div class="money">
                <span>¥</span>
                <span>{{info.detail.price}}</span>
              </div>
              <div class="nums">
                <span>{{info.detail.purchase_count}}人购买</span>
              </div>
            </div>
            <shareCard :title="info.detail.title" :image="isHttpOrHttps(info.detail.cover_image)">
              <div class="tool">
                <template v-if="info.detail.price > 0">
                  <template v-if="info.detail.has_purchased">
                    <el-button type="success" @click="handleStudy">立即学习</el-button>
                  </template>
                  <template v-else>
                    <el-button type="primary" @click="handleOperate">购买</el-button>
                  </template>
                </template>
                <template v-else>
                  <el-button type="primary" @click="handleOperate">免费咨询</el-button>
                </template>
                <el-button color="#EEAF3D" :icon="info.detail?.is_collected ? StarFilled : Star" @click="handleCollect">收藏</el-button>
              </div>
            </shareCard>
            <div class="card">
              <div class="part flex align-center">
                <div class="name">
                  <span>服务保障</span>
                </div>
                <div class="tips flex align-center">
                  <span>一对一服务 · 可加急 · 无忧售后 · 提供发票 · 极速退款</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>服务保障</span>
                </div>
                <div class="tips flex align-center">
                  <img src="@/assets/images/common/icon_wxpay.png" alt="">
                  <img src="@/assets/images/common/icon_alipay.png" alt="">
                  <img src="@/assets/images/common/icon_bankcard.png" alt="">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="contents">
      <div class="container flex justify-between">
        <div class="info flex-1">
          <div class="content bg-white">
            <div class="name-bar flex align-center justify-center">
              <img src="@/assets/images/home/<USER>" alt="">
              <span>服务详情</span>
              <img src="@/assets/images/home/<USER>" alt="">
            </div>
            <div class="html" v-html="info.detail.service_desc"></div>
          </div>
          <div class="about" v-if="info.related.experts?.length">
            <aboutCard title="相关专家" :icon="getAssetsFile('common/icon_zj.png')" moreUrl="/expert">
              <div class="items">
                <ExpertCard :info="item" v-for="(item, index) in info.related.experts" :key="index" />
              </div>
            </aboutCard>
          </div>
          <div class="about" v-if="info.related.journals?.length">
            <aboutCard title="相关书籍" :icon="getAssetsFile('common/icon_books.png')" moreUrl="/journal">
              <div class="items">
                <JournalCard :info="item" v-for="(item, index) in info.related.journals" :key="index" />
              </div>
            </aboutCard>
          </div>
        </div>
        <div class="slider">
          <div class="card">
            <friendCard />
          </div>
          <div class="card" v-if="info.reviews?.length">
            <otherCard title="真实用户评价" :icon="getAssetsFile('common/icon_edit.png')">
              <div class="evaluates">
                <div class="evaluate" v-for="(item, index) in info.reviews" :key="index">
                  <div class="title flex align-center justify-between">
                    <div class="_left">
                      <span>{{item.user?.nickname}}</span>
                    </div>
                    <div class="_right">
                      <span>{{dayjs(item.create_time*1000).format('YY年MM月DD日')}}</span>
                    </div>
                  </div>
                  <div class="stars">
                    <el-icon v-for="i in item.rating">
                      <StarFilled color="#EEAF3D" />
                    </el-icon>
                  </div>
                  <div class="say" v-html="item.content"></div>
                </div>
              </div>
            </otherCard>
          </div>
          <div class="card">
            <otherCard title="相关推荐" :icon="getAssetsFile('common/icon_zan.png')" more-url="/service">
              <div class="services">
                <div class="service flex align-center justify-center" v-for="(item, index) in info.recommended" :key="index">
                  <serviceCard :info="item" />
                </div>
              </div>
            </otherCard>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
    <el-dialog
        v-model="dialogFormVisible"
        width="800"
        title="请输入信息"
        class="buyFormPop"
        :show-close="false"
    >
      <div class="buy-form">
        <el-form ref="ruleFormRef" :model="buyForm" :rules="rules" label-position="left" label-width="auto">
          <el-form-item label="联系人" prop="name">
            <el-input v-model="buyForm.name" placeholder="请输入联系人" />
          </el-form-item>
          <el-form-item label="电话" prop="phone">
            <el-input v-model="buyForm.phone" placeholder="请输入电话" />
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="buyForm.email" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="支付方式">
            <el-radio-group v-model="buyForm.type">
              <div class="pays flex align-center justify-between flex-1">
                <el-radio value="alipay">
                  <div class="pay flex align-center">
                    <img src="@/assets/images/common/icon_alipay_pay.png" alt="">
                    <span>支付宝扫码付款</span>
                  </div>
                </el-radio>
                <el-radio value="wechat">
                  <div class="pay flex align-center">
                    <img src="@/assets/images/common/icon_wechat_pay.png" alt="">
                    <span>微信扫码付款</span>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submit(ruleFormRef)">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <!-- 咨询弹窗 -->
    <el-dialog
        v-model="dialogConsultationVisible"
        width="800"
        title="免费咨询"
        class="consultationFormPop"
        :show-close="false"
    >
      <div class="consultation-form">
        <div class="consultation-tip">
          请填写您的联系方式，我们的专业导师将在二十四小时内联系您。
        </div>
        <el-form ref="consultationFormRef" :model="consultationForm" :rules="consultationRules" label-position="left" label-width="auto">
          <el-form-item label="联系人" prop="contact_name">
            <el-input v-model="consultationForm.contact_name" placeholder="请输入联系人" />
          </el-form-item>
          <el-form-item label="手机号" prop="contact_mobile">
            <el-input v-model="consultationForm.contact_mobile" placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="consultationForm.contact_email" placeholder="请输入邮箱（选填）" />
          </el-form-item>
          <el-form-item label="专业方向">
            <el-input v-model="consultationForm.specialization" placeholder="请输入专业方向（选填）" />
          </el-form-item>
          <el-form-item label="验证码" prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="consultationForm.captcha"
                placeholder="请输入验证码"
                style="width: 200px; margin-right: 10px;"
              />
              <div
                class="captcha-image"
                @click="refreshCaptcha"
                title="点击刷新验证码"
              >
                <img
                  v-if="captchaUrl"
                  :src="captchaUrl"
                  alt="验证码"
                  style="width: 100%; height: 100%;"
                />
                <span v-else style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">点击获取</span>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitConsultation(consultationFormRef)">提交咨询</el-button>
            <el-button @click="dialogConsultationVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog
        v-model="dialogStudyVisible"
        width="460"
        class="studyPop"
        :show-close="false"
    >
      <div class="study-info">
        <div class="title flex align-center justify-center">
          <img src="@/assets/images/common/icon_study_left.png" alt="">
          <div class="name">立即学习</div>
          <img src="@/assets/images/common/icon_study_right.png" alt="">
        </div>
        <div class="code">
          <img :src="isHttpOrHttps(systemStore.kefu_qrcode)" alt="">
        </div>
        <div class="tips flex justify-center">
          <span>扫描二维码联系助理老师进行学习</span>
        </div>
      </div>
    </el-dialog>
    <Pay ref="payRef" :orderInfo="orderInfo" @close="handleClosePop" />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .introduce{
    .container{
      .box{
        margin: px2rem(24) 0 px2rem(20);
        box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
        border-radius: px2rem(16);
        padding: px2rem(34) px2rem(24) px2rem(24);
        box-sizing:  border-box;
        .left{
          width: px2rem(348);
          height: px2rem(220);
          border-radius: px2rem(20);
          img{
            width: 100%;
          }
        }
        .right{
          margin-left: px2rem(26);
          .title{
            font-size: px2rem(26);
            color: #1F1F1F;
            line-height: 1;
            font-weight: 600;
          }
          .desc{
            margin: px2rem(14) 0 px2rem(22);
            color: #5C6671;
            font-size: px2rem(16);
            line-height: px2rem(22);
          }
          .prices{
            .money{
              color: #E64F26;
              font-weight: 500;
              span{
                &:first-child{
                  font-size: px2rem(24);
                  padding-right: px2rem(5);
                }
                &:last-child{
                  font-size: px2rem(30);
                }
              }
            }
            .nums{
              font-size: px2rem(16);
              color: #696A77;
              font-weight: 400;
            }
          }
          .tool{
            .el-button{
              &:first-child{
                width: px2rem(182);
                height: px2rem(52);
                font-size: px2rem(20);
                border-radius: px2rem(27);
              }
              &:last-child{
                background-color: transparent;
                width: px2rem(145);
                height: px2rem(52);
                font-size: px2rem(20);
                border-radius: px2rem(26);
                color: #EEAF3D;
              }
            }
          }
          .card{
            margin-top: px2rem(20);
            background-color: #F4F6F9;
            border-radius: px2rem(8);
            padding: px2rem(24);
            box-sizing: border-box;
            .part{
              &+.part{
                margin-top: px2rem(20);
              }
              .name{
                color: #5C6671;
              }
              .tips{
                margin-left: px2rem(27);
                color: #191B1F;
                font-size: px2rem(16);
                img{
                  width: px2rem(28);
                  height: px2rem(28);
                  &+img{
                    margin-left: px2rem(18);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .contents{
    .container{
      .info{
        margin-right: px2rem(20);
        .content{
          box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
          border-radius:  px2rem(16);
          padding: 0 px2rem(24) px2rem(40);
          box-sizing: border-box;
          .name-bar{
            color: #3B78C8;
            font-weight: bold;
            font-size: px2rem(26);
            padding: px2rem(40) 0;
            box-sizing: border-box;
            span{
              padding: 0 px2rem(30);
              box-sizing: border-box;
            }
            img{
              width: px2rem(74);
              height: px2rem(6);
            }
          }
          .html{
            img{
              width: 100%;
            }
          }
        }
        .about{
          margin-top: px2rem(60);

          .items{
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: px2rem(16);
          }
        }
      }
      .slider{
        width: px2rem(353);
        .card{
          &+.card{
            margin-top: px2rem(20);
          }
          .evaluates{
            .evaluate{
              .title{
                font-size: px2rem(16);
                line-height: 1;
                ._left{
                  color: #191B1F;
                }
                ._right{
                  color: #909090;
                }
              }
              .stars{
                margin: px2rem(8) 0 px2rem(16);
              }
              .say{
                color: #1F1F1F;
                font-size: px2rem(14);
                font-weight: 400;
                line-height: px2rem(20);
              }
            }
          }
          .services{
            .service{
              background-color: #F4F6F9;
              padding: px2rem(20);
              box-sizing: border-box;
              border-radius: px2rem(8);
              &+.service{
                margin-top: px2rem(20);
              }
            }
          }
        }
      }
    }
  }
  .empty{
    margin-top: px2rem(100);
  }
  .buy-form{
    padding: 0 px2rem(70);
    box-sizing:  border-box;
    :deep(.el-form){
      .el-form-item{
        margin-bottom: px2rem(30);
        &:last-child{
          margin-top: px2rem(70);
          margin-bottom: 0;
          .el-form-item__content{
            justify-content: center;
          }
        }
        .el-form-item__label-wrap{
          .el-form-item__label{
            color: #535871;
            font-size: px2rem(18);
            padding-right: px2rem(20);
            height: px2rem(45);
            line-height: px2rem(45);
          }
        }
        .el-form-item__content{
          .el-form-item__error{
            font-size: 14px;
          }
          .el-input{
            .el-input__wrapper{
              box-shadow: unset;
              background-color: #F7FAFE;
              padding: 0 px2rem(24);
              border-radius: px2rem(8);
              .el-input__inner{
                height: px2rem(45);
                font-size: px2rem(18);
              }
            }
          }
          .el-radio-group{
            .pays{
              padding: 0 px2rem(55) 0 px2rem(40);
              box-sizing: border-box;
              .el-radio{
                margin-right: px2rem(102);
                .el-radio__input{
                  .el-radio__inner{
                    width: px2rem(24);
                    height: px2rem(24);
                    &::after{
                      width: px2rem(10);
                      height: px2rem(10);
                    }
                  }
                }
                .el-radio__label{
                  padding-left: px2rem(16);
                  .pay{
                    img{
                      width: px2rem(28);
                      margin-right: px2rem(10);
                    }
                  }
                }
              }
            }
          }
        }
        .el-button{
          width: px2rem(200);
          height: px2rem(58);
          border-radius: px2rem(8);
          font-size: px2rem(20);
        }
      }
    }
  }
  .study-info{
    .title{
      .name{
        font-size: px2rem(24);
        color: #36B96B;
        font-weight: 600;
        margin: 0 px2rem(22);
      }
      img{
        width: px2rem(49);
      }
    }
    .code{
      width: px2rem(158);
      margin: px2rem(60) auto;
      img{
        width: 100%;
      }
    }
    .tips{
      color: #36B96B;
      font-weight: 600;
      font-size: px2rem(24);
    }
  }
}

// 咨询弹窗样式
:deep(.consultationFormPop) {
  .el-dialog__body {
    padding: px2rem(20) px2rem(30);
  }

  .consultation-form {
    .consultation-tip {
      background: #f0f9ff;
      border: 1px solid #e1f5fe;
      border-radius: px2rem(8);
      padding: px2rem(16);
      margin-bottom: px2rem(24);
      color: #0277bd;
      font-size: px2rem(14);
      line-height: 1.5;
      text-align: center;
    }

    .el-form-item {
      margin-bottom: px2rem(20);

      .el-form-item__label {
        font-size: px2rem(16);
        color: #333;
        font-weight: 500;
      }

      .el-input__wrapper {
        border-radius: px2rem(8);
      }

      .el-textarea__inner {
        border-radius: px2rem(8);
        min-height: px2rem(100);
      }
    }

    .captcha-container {
      display: flex;
      align-items: center;

      .captcha-image {
        width: px2rem(120);
        height: px2rem(40);
        border: 1px solid #dcdfe6;
        border-radius: px2rem(4);
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409EFF;
        }
      }
    }

    .el-button {
      height: px2rem(40);
      border-radius: px2rem(8);
      font-size: px2rem(16);
      margin-right: px2rem(10);

      &[type="primary"] {
        background: #409EFF;
        border-color: #409EFF;
      }
    }
  }
}
</style>