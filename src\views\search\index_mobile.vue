<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import {backPage} from "@/utils/router.ts";
import serviceCardMobile from "@/components/serviceCardMobile.vue";
import expertCardMobile from "@/components/expertCardMobile.vue";
import journalCardMobile from '@/components/journalCardMobile.vue';
import storiesCardMobile from "@/components/storiesCardMobile.vue";
import paginateMobile from "@/components/paginateMobile.vue";
import { useRoute, useRouter } from 'vue-router'
import {commonSerach} from "@/api/common.ts";
const route = useRoute();
const router = useRouter();
const keywords = ref('')
const tabs = ref([
  {name: '全部', value: ''},
  {name: '学术科研服务', value: 'SERVICE'},
  {name: '专家智库', value: 'EXPERT'},
  {name: '学术期刊', value: 'JOURNAL'},
  {name: '学术头条', value: 'HEADLINE'},
])
const tabCur = ref(0)
const total = ref(0)
const page = ref(1)
const limit = ref(10)
const list = ref([])

const getList = () => {
  if (!route.query.keyword) {
    total.value = 0
    list.value = []
    return
  }
  commonSerach({
    page: page.value,
    keyword: route.query.keyword,
    type: tabs.value[tabCur.value].value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getList()
}
const handleSearch = () => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      keyword: keywords.value
    }
  })
}
watch(tabCur, (val) => {
  page.value = 1
  getList()
})
watch(
    () => route.query.keyword,
    () => {
      page.value = 1
      getList()
    }
)
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 flex flex-column hidden">
      <div class="search flex align-center justify-between">
        <div class="icon">
          <img class="flex" src="@/assets/images/common_mobile/icon_search1.png" alt="">
        </div>
        <el-input class="flex-1" v-model="keywords" placeholder="请输入" @keydown.enter="handleSearch" />
        <el-button color="var(--maincolor)" @click="handleSearch">搜索</el-button>
      </div>
      <div class="tabs flex align-center justify-between">
        <div class="tab" :class="{active: tabCur === index}" @click="tabCur = index" v-for="(item, index) in tabs" :key="index">
          <span>{{item.name}}</span>
        </div>
      </div>
      <div class="items flex-1">
        <div class="part" v-for="(item, index) in list" :key="index">
          <template v-if="item.content_type === 'SERVICE'">
            <serviceCardMobile :info="item" />
          </template>
          <template v-else-if="item.content_type === 'EXPERT'">
            <expertCardMobile :info="item" />
          </template>
          <template v-else-if="item.content_type === 'JOURNAL'">
            <journalCardMobile :info="item" />
          </template>
          <template v-else-if="item.content_type === 'HEADLINE'">
            <storiesCardMobile :info="item" />
          </template>
        </div>
      </div>
      <div class="paginates">
        <paginateMobile :limit="limit" :total="total" @change="changePage" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #F6F7FA;
  height: 100vh;

  .box {
    padding: 12px 15px;
    box-sizing: border-box;
    .search{
      border: 1px solid $maincolor;
      border-radius: 30px;
      padding: 3px;
      box-sizing: border-box;
      .icon{
        padding: 6px 10px 6px 12px;
        box-sizing: border-box;
        img{
          width: 16px;
        }
      }
      :deep(.el-input){
        .el-input__wrapper{
          box-shadow: none;
          background-color: transparent;
          padding: 1px 0;
          .el-input__inner{
            font-size: 14px;
          }
        }
      }
      .el-button{
        border-radius: 14px;
        font-size: 14px;
      }
    }
    .tabs{
      margin: 16px 0;
      .tab{
        color: #535871;
        font-size: 13px;
        &.active{
          color: $maincolor;
        }
      }
    }
    .items{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px 11px;
      grid-auto-rows: min-content;
    }
    .paginates{
      margin-top: 10px;
    }
  }
}
</style>