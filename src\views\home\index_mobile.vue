<script setup lang="ts">
import {onMounted, onUnmounted, ref} from "vue";
import { useSystemStore } from '@/store/system.ts'
import {vue3ScrollSeamless} from "vue3-scroll-seamless";
import HeaderMobile from "@/components/headerMobile.vue";
import JournalCardMobile from "@/components/journalCardMobile.vue";
import ServiceCardMobile from "@/components/serviceCardMobile.vue";
import ExpertCardMobile from "@/components/expertCardMobile.vue";
import {ArrowRight} from "@element-plus/icons-vue";
import {safePush} from "@/utils/router.ts";
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
const systemStore = useSystemStore();
const heightimg = ref('0px');
const classOptions = {
  limitMoveNum: 4,
  direction: 2,
  step: 0.5,
  hoverStop: true
};
const customerOutcomes = ref([1,2,3,4,5,6,7,8,9,10])
const tabs = ref(['推荐服务', '推荐专家', '推荐期刊'])
const tabCur = ref(0)
const services = ref([
  {
    name: '本科阶段',
    desc: '科研入门引导、学业规划制定、基础技能训练，夯实科研基础，增强升学就业核心竞争力',
    more: '更多',
    icon: getAssetsFile('home/service_1.png'),
    url: '',
    backgroundColor: '#D6F0FF'
  },
  {
    name: '硕士阶段',
    desc: '论文写作攻坚、研究成果产出、发展方向明晰，保障论文通过，强化申博就业硬核实力',
    more: '更多',
    icon: getAssetsFile('home/service_2.png'),
    url: '',
    backgroundColor: '#D3DBFE'
  },
  {
    name: '博士阶段',
    desc: '核心成果培育、学术资源对接，加速成长进程，构筑职业发展高端优势',
    more: '更多',
    icon: getAssetsFile('home/service_3.png'),
    url: '',
    backgroundColor: '#FFD5FC'
  },
  {
    name: '在职阶段',
    desc: '职称晋升规划、学用知识融合、前沿动态传递，助力职称提升，以学术推动职业进阶',
    more: '更多',
    icon: getAssetsFile('home/service_4.png'),
    url: '',
    backgroundColor: '#E7D9FF'
  },
])
const handleResize = ()=>{
  heightimg.value = document.getElementsByClassName('imgs')[0].offsetHeight + 'px'
}
onMounted(() => {
  setTimeout(()=>{
    heightimg.value = document.getElementsByClassName('imgs')[0].offsetHeight + 'px'
  },500)
  window.addEventListener('resize',handleResize)
})
onUnmounted(()=>{
  window.removeEventListener('resize',handleResize)
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="flex-1 auto">
      <div class="banners">
        <el-carousel motion-blur :interval="10000" :height="heightimg">
          <el-carousel-item v-for="(item, index) in systemStore.site.banner" :key="index">
            <img class="imgs" :src="isHttpOrHttps(item)" alt="" style="width: 100%">
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="recommend">
        <div class="card-menus flex align-center justify-between">
          <el-link underline="never" class="menu" v-for="(item, index) in systemStore.site.category" :key="index" @click="safePush({path: '/service/index', query: {category_id: item.id}})">
            <el-image :src="isHttpOrHttps(item.image)"></el-image>
          </el-link>
        </div>
        <div class="tabs flex align-center">
          <div class="tab flex flex-column align-center" :class="{active: tabCur === index}" v-for="(item, index) in tabs" :key="index" @click="tabCur = index">
            <div class="name">
              <span>{{ item }}</span>
            </div>
            <div class="icon">
              <template v-if="tabCur === index">
                <img class="flex" src="@/assets/images/home_mobile/icon_menu_active.png" alt="">
              </template>
            </div>
          </div>
        </div>
        <div class="items">
          <template v-if="tabCur === 0">
            <ServiceCardMobile :info="item" v-for="(item, index) in systemStore.site.recommended.services" :key="index"/>
          </template>
          <template v-else-if="tabCur === 1">
            <ExpertCardMobile :info="item" v-for="(item, index) in systemStore.site.recommended.experts" :key="index" />
          </template>
          <template v-else-if="tabCur === 2">
            <JournalCardMobile :info="item" v-for="(item, index) in systemStore.site.recommended.journals" :key="index" />
          </template>
        </div>
      </div>
      <div class="support">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab1_title}}</span>
        </div>
        <div class="desc text-center">
          <span>{{systemStore.site.home.tab1_desc}}</span>
        </div>
        <div class="img flex justify-center">
          <el-image :src="getAssetsFile('home_mobile/bg_support.png')"></el-image>
        </div>
      </div>
      <div class="services position-relative">
        <div class="bg position-absolute">
          <el-image :src="getAssetsFile('home_mobile/bg_service.png')"></el-image>
        </div>
        <div class="title text-center position-relative">
          <span>{{systemStore.site.home.tab2_title}}</span>
        </div>
        <div class="desc text-center position-relative">
          <span>{{systemStore.site.home.tab2_desc}}</span>
        </div>
        <div class="service flex align-center justify-between position-relative">
          <div class="item flex flex-column align-center" :style="{backgroundColor: item.backgroundColor}"
               v-for="(item, index) in services" :key="index">
            <el-image :src="item.icon"></el-image>
            <div class="name">
              <span>{{ item.name }}</span>
            </div>
            <div class="description">
              <span>{{ item.desc }}</span>
            </div>
            <el-link underline="never" class="more flex align-center" @click="safePush('/service')">
              <span>{{ item.more }}</span>
              <el-icon>
                <ArrowRight/>
              </el-icon>
            </el-link>
          </div>
        </div>
      </div>
      <div class="study">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab3_title}}</span>
        </div>
        <div class="description text-center">
          <span>{{systemStore.site.home.tab3_desc}}</span>
        </div>
        <div class="items">
          <div class="item hidden position-relative"
               :style="{background: `url(${isHttpOrHttps(item.cover_image)}) no-repeat`, backgroundSize: '100% 100%'}"
               v-for="(item, index) in systemStore.site.HEADLINE.featured" :key="index" @click="safePush({path: '/stories/details', query:{id: item.id}})">
            <div class="name row1 text-white position-absolute">
              <span>{{item.title}}</span>
            </div>
          </div>
        </div>
        <div class="hot">
          <div class="titles flex align-center">
            <el-image :src="getAssetsFile('home/icon_fire.png')"></el-image>
            <span>热门文章</span>
          </div>
          <div class="ranks">
            <el-link underline="never" class="rank flex align-center justify-start" v-for="(item, index) in systemStore.site.HEADLINE.list" :key="index" @click="safePush({path: '/stories/details', query:{id: item.id}})">
              <span class="text-center">{{ index + 1 }}</span>
              <span class="row1">{{item.title}}</span>
            </el-link>
          </div>
        </div>
      </div>
      <div class="achievement">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab5_title}}</span>
        </div>
        <div class="desc text-center">
          <span>{{systemStore.site.home.tab5_desc}}</span>
        </div>
        <vue3ScrollSeamless
            class="hidden"
            :classOptions="classOptions"
            :dataList="customerOutcomes"
        >
          <div class="items flex align-center">
            <div class="item position-relative bg-white" v-for="(item, index) in systemStore.site.customer_result" :key="index">
              <div class="tag position-absolute">
                <img src="@/assets/images/home/<USER>" alt="">
              </div>
              <div class="name flex align-center">
                <img src="@/assets/images/home/<USER>" alt="">
                <span>客户成果</span>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>期刊名称</span>
                </div>
                <div class="value flex-1">
                  <span>《{{item.journal_name ?? '-'}}》</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>影响因子</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.impact_factor ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>作者信息</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.author_info ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>服务项目</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.service_project ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>成果描述</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.description ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>发表日期</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.publication_date ?? '-'}}</span>
                </div>
              </div>
            </div>
          </div>
        </vue3ScrollSeamless>
      </div>
      <div class="school">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab4_title}}</span>
        </div>
        <div class="desc text-center">
          <span>{{systemStore.site.home.tab4_desc}}</span>
        </div>
        <div class="links">
          <div class="link hidden" v-for="(item, index) in systemStore.site?.firind_link" :key="index">
            <el-image :src="isHttpOrHttps(item.image)" fit="scale-down" @click="jumpOutWeb(item.url)"></el-image>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page{
  background: #F6F7FA;
  height: 100vh;
  .recommend{
    margin: 15px;
    .card-menus{
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 10px;
    }
    .tabs{
      margin-top: 15px;
      .tab{
        font-size: 16px;
        color: #535871;
        &+.tab{
          margin-left: 16px;
        }
        &.active{
          color: $maincolor;
        }
        .icon{
          width: 20px;
          height: 3px;
          margin-top: 3px;
          img{
            width: 100%;
          }
        }
      }
    }
    .items{
      margin-top: 9px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px;
    }
  }
  .support {
    width: 100%;
    padding: 30px 15px;
    box-sizing: border-box;
    .title {
      color: $maincolor;
      font-size: 18px;
      font-weight: bold;
    }

    .desc {
      width: 100%;
      margin: 10px auto 20px;
      font-size: 12px;
      color: #909090;
      line-height: 22px;
      font-weight: 400;
    }

    .el-image {
      width: 325px;
    }
  }
  .services {
    margin: 0 15px;
    box-sizing: border-box;
    .bg {
      z-index: 1;
      top: 110px;
      width: 100%;
    }

    .title {
      font-size: 18px;
      font-weight: bold;
      color: $maincolor;
      z-index: 2;
      width: 200px;
      margin: 0 auto;
    }

    .desc {
      width: 100%;
      font-size: 12px;
      color: #909090;
      margin: 10px auto 20px;
      line-height: 22px;
      z-index: 2;
    }

    .service {
      z-index: 2;
      .item {
        width: 80px;
        height: 239px;
        border-radius: 8px;
        padding: 0 7px;
        box-sizing: border-box;

        .el-image {
          width: 53px;
          height: 53px;
        }

        .name {
          color: #1F1F1F;
          font-size: 14px;
        }

        .description {
          font-size: 12px;
          color: #5A5A5A;
          height: 115px;
          margin: 4px 0 px2rem(12);
        }

        .more {
          font-size: 12px;
          color: #909090;
          font-weight: normal;
          margin-top: 13px;
          span{
            line-height: 1;
          }
        }
      }
    }
  }
  .study{
    padding: 30px 15px 0;
    box-sizing: border-box;
    .title {
      font-size: 18px;
      font-weight: bold;
      color: $maincolor;
      z-index: 2;
      margin: 0 auto;
    }
    .description {
      width: 100%;
      font-size: 12px;
      color: #909090;
      margin: 10px auto 20px;
      line-height: 22px;
      z-index: 2;
    }
    .items{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 11px;
      .item {
        width: 100%;
        height: 123px;
        border-radius: 12px;

        .name {
          left: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          height: 27px;
          line-height: 27px;
          font-size: 12px;
          padding: 0 13px 0 10px;
          box-sizing: border-box;
          width: 100%;
        }
      }
    }
    .hot{
      margin-top: 12px;
      box-shadow: 0 1px 13px 0 rgba(66, 88, 241, 0.1);
      border-radius: 12px;
      width: 100%;
      padding: 15px 12px;
      box-sizing: border-box;

      .titles {
        padding-bottom: 12px;
        border-bottom: 1px solid #E4E6EA;
        color: $maincolor;

        .el-image {
          width: 18px;
          height: 18px;
          margin-right: 6px;
          font-size: 16px;
        }
      }

      .ranks {
        padding-top: 15px;
        box-sizing: border-box;

        .rank {
          font-size: 14px;

          & + .rank {
            margin-top: 20px;
          }

          span {
            font-weight: normal;

            &:first-child {
              color: #C4C4C9;
              padding-right: 9px;
              font-family: YouSheBiaoTiYuan, serif;
              width: 15px;
              font-size: 16px;
            }
          }

          &:nth-child(1) {
            span {
              &:first-child {
                color: #D6635E;
              }
            }
          }

          &:nth-child(2) {
            span {
              &:first-child {
                color: #E59A6B;
              }
            }
          }

          &:nth-child(3) {
            span {
              &:first-child {
                color: #F1C363;
              }
            }
          }
        }
      }
    }
  }
  .achievement{
    padding: 30px 15px 0;
    box-sizing: border-box;
    .title {
      font-size: 18px;
      font-weight: bold;
      color: $maincolor;
      z-index: 2;
      margin: 0 auto;
    }
    .desc {
      width: 100%;
      font-size: 12px;
      color: #909090;
      margin: 10px auto 20px;
      line-height: 22px;
      z-index: 2;
    }
    .items {
      margin: 2px 0;
      .item {
        box-shadow: 0 1px 8px 0 rgba(66, 88, 241, 0.14);
        border-radius: 12px;
        width: 230px;
        padding: 14px 12px;
        box-sizing: border-box;
        margin-right: 12px;

        .tag {
          top: 0;
          right: 12px;
          width: 19px;
          height: 29px;
          img{
            width: 100%;
          }
        }

        .name {
          font-size: 14px;
          margin-bottom: 14px;

          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }

        .part {
          & + .part {
            margin-top: 6px;
          }

          ._name {
            font-size: 12px;
            color: #909090;
            width: 50px;
            margin-right: 10px;
          }

          .value {
            font-size: 12px;
            word-break: break-all;
          }
        }
      }
    }
  }
  .school{
    padding: 30px 15px 40px;
    box-sizing: border-box;
    .title {
      font-size: 18px;
      font-weight: bold;
      color: $maincolor;
      z-index: 2;
      margin: 0 auto;
    }
    .desc {
      width: 100%;
      font-size: 12px;
      color: #909090;
      margin: 10px auto 20px;
      line-height: 22px;
      z-index: 2;
    }
    .links {
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 13px 7px;

      .link {
        width: 81px;
        height: 35px;
        box-shadow: 0 3px 8px 0 rgba(66, 88, 241, 0.12);
        border-radius: 4px;
      }
    }
  }
}

</style>