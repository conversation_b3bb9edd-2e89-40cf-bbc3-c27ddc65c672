<script setup lang="ts">

import { useSystemStore } from '@/store/system.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
const systemStore = useSystemStore()
</script>

<template>
  <div class="friend-card bg-white flex flex-column align-center justify-center">
    <img :src="isHttpOrHttps(systemStore.kefu_qrcode)" alt="">
    <div class="tips">
      <span>立即添加好友，了解更多</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.friend-card{
  width: 100%;
  img{
    width: 100px;
  }
  .tips{
    margin-top: 16px;
    color: #191B1F;
    font-size: 14px;
  }
}
</style>