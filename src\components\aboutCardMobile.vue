<script setup lang="ts">
import {safePush} from "@/utils/router.ts";

const props = defineProps({
  title: {
    type: String,
    required: true,
    default: '',
  },
  icon: {
    type: String,
    required: false,
    default: '',
  },
  moreUrl: {
    type: String,
    required: false,
    default: '',
  }
})
</script>

<template>
  <div class="abouts">
    <div class="name-bar flex align-center">
      <img :src="props.icon" alt="">
      <span>{{props.title}}</span>
    </div>
    <slot />
  </div>
</template>

<style scoped lang="scss">
.abouts{
  .name-bar{
    color: #191B1F;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    img{
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }
  }
}
</style>