import router from './router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

NProgress.configure({
  showSpinner: false
})

router.beforeEach((to, from, next) => {
  NProgress.start()
  if (to.path === from.path && JSON.stringify(to.query) === JSON.stringify(from.query)) {
    return
  }
  if (to.meta.title) {
    document.title = to.meta.title + '-' + import.meta.env.VITE_WEBNAME
  }
  next()
})

router.afterEach(() => {
  NProgress.done()
})
