<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { journalList, getSubjectOptions } from '@/api/common.ts'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import paginate from "@/components/paginate.vue";
import JournalCard from "@/components/journalCard.vue";
import {getAssetsFile, isHttpOrHttps} from "@/utils/utils.ts";
import { useSystemStore } from '@/store/system.ts'
import {safePush} from "@/utils/router.ts";
const systemStore = useSystemStore()
const limit = ref(10)
const pageInfo = ref({
  title: '',
  subtitle: '',
})
const filters = ref({
  big_type: '',
  journal_datatype: '',
  journal_jcr: '',
  journal_type: '',
  journal_zky: '',
  keyword: '',
  small_type: '',
  impact_factor: ['','']
})
const options = ref([
  {
    name: '大类学科',
    field: 'big_type',
    type: 'select',
    options: []
  },
  {
    name: '小类学科',
    field: 'small_type',
    type: 'select',
    options: []
  },
  {
    name: '期刊类型',
    field: 'journal_type',
    type: 'select',
    options: []
  },
  {
    name: '数据库',
    field: 'journal_datatype',
    type: 'select',
    options: []
  },
  {
    name: '中科院',
    field: 'journal_zky',
    type: 'select',
    options: []
  },
  {
    name: 'JCR',
    field: 'journal_jcr',
    type: 'select',
    options: []
  },
  {
    name: '影响因子',
    field: 'impact_factor',
    type: 'range',
    placeholder: ['大于因子', '小于因子']
  },
  {
    name: '关键词',
    field: 'keyword',
    type: 'input',
    placeholder: '支持模糊检索，多关键词组合',
  }
])
const page = ref(1)
const total = ref(0)
const list = ref([])
const getJournalList = () => {
  journalList({
    ...filters.value,
    page: page.value,
    limit: limit.value,
  }).then(res => {
    pageInfo.value = res.data.pageInfo
    total.value = res.data.pagination.total
    list.value = res.data.list
    for(let i = 0; i < options.value.length; i++) {
      if (res.data.options[options.value[i].field]) {
        options.value[i].options = res.data.options[options.value[i].field]
      }
    }
  })
}
const changePage = (e: number) => {
  page.value = e
  getJournalList()
}
const handleSearch = () => {
  page.value = 1
  getJournalList()
}
const handleReset = () => {
  for(let i in filters.value) {
    filters.value[i] = ''
  }
  filters.value.impact_factor = ['', '']
  handleSearch()
}
const handleChange = (e: any, field: string) => {
  if (field === 'big_type') {
    filters.value['small_type'] = ''
    options.value[1].options = []
    getSubjectOptions({
      parent_id: e
    }).then(res => {
      options.value[1].options = res.data.list.map(item => {
        return {
          key: item.id,
          label: item.name
        }
      })
    })
  }
}
const handleQK = (e: any) => {
  let path = ''
  switch(e.content_type){
    case 'SERVICE':
      path = "/service/details"
      break;
    case 'EXPERT':
      path = "/expert/details"
      break;
  }
  safePush({
    path: path,
    query: {
      id: e.remoteValue
    }
  })
}
onMounted(() => {
  getJournalList()
})
</script>

<template>
  <div class="main">
    <Header />
    <div class="parts">
      <div class="container flex align-center justify-between">
        <div class="part cursor-pointer" v-for="(item, index) in systemStore.qk_image" :key="index" @click="handleQK(item)">
          <img :src="isHttpOrHttps(item.image)" alt="">
        </div>
      </div>
    </div>
    <div class="boxs">
      <div class="container bg-white">
        <div class="filters">
          <div class="filter flex align-center justify-between" v-for="(item, index) in options" :key="index">
            <div class="name">{{item.name}}</div>
            <div class="form flex-1 flex align-center">
              <template v-if="item.type === 'select'">
                <el-select v-model="filters[item.field]" clearable @change="handleChange($event, item.field)">
                  <el-option :label="item1.label" :value="item1.key" v-for="(item1, index1) in item.options" />
                </el-select>
              </template>
              <template v-else-if="item.type === 'range'">
                <el-input :placeholder="item.placeholder[0]" v-model="filters[item.field][0]" clearable></el-input>
                <div class="ml-1 mr-1">
                  <span>-</span>
                </div>
                <el-input :placeholder="item.placeholder[1]" v-model="filters[item.field][1]" clearable></el-input>
              </template>
              <template v-else-if="item.type === 'input'">
                <el-input :placeholder="item.placeholder" v-model="filters[item.field]" clearable></el-input>
              </template>
            </div>
          </div>
          <div class="filter flex align-center justify-between">
            <div class="name"></div>
            <div class="form flex-1">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </div>
          </div>
        </div>
        <div class="list">
          <JournalCard :info="item" is-flex :img-width="150" :img-height="200" v-for="(item, index) in list" :key="index" />
        </div>
      </div>
    </div>
    <div class="paginates">
      <div class="container">
        <paginate :limit="limit" :total="total" @change="changePage" />
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .parts{
    margin: px2rem(40) 0;
    .container{
      .part{
        width: px2rem(158);
        height: px2rem(96);
        img{
          width: 100%;
        }
      }
    }
  }
  .boxs{
    .container{
      box-shadow: 0 px2rem(2) px2rem(8) 0 rgba(0,66,190,0.08);
      border-radius: px2rem(24);
      padding: px2rem(26) px2rem(30) px2rem(35);
      box-sizing:  border-box;
      .filters{
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: px2rem(24) px2rem(30);
        .filter{
          .name{
            width: px2rem(60);
            color: #434864;
            font-size: px2rem(14);
          }
          .form{
            margin-left: px2rem(20);
            height: px2rem(40);
            :deep(.el-select){
              width: 100%;
              height: 100%;
              .el-select__wrapper{
                line-height:  1;
                height: 100%;
                min-height: unset;
                font-size: px2rem(14);
              }
            }
            :deep(.el-input){
              height: 100%;
              font-size: px2rem(14);
            }
            :deep(.el-button){
              width: px2rem(134);
              height: px2rem(30);
              font-size:  px2rem(14);
            }
          }
        }
      }
      .list{
        margin: px2rem(40) px2rem(5) 0;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: px2rem(30);
      }
    }
  }
  .paginates{
    margin: px2rem(40) 0 px2rem(100);
  }
}
</style>