<script setup lang="ts">
import {ref, onMounted, watch} from 'vue'
import HeaderMobile from "@/components/headerMobile.vue";
import {contentDetail, toggleCollect, toggleLike} from '@/api/common.ts'
import {useRoute} from 'vue-router'
import {useSystemStore} from '@/store/system'
import {ElMessage} from "element-plus";
import {getAssetsFile, shareToQQ, shareToWeibo} from "@/utils/utils.ts";
import OtherCard from "@/components/otherCard.vue";
import AdCard from "@/components/adCard.vue";
import OtherCardMobile from "@/components/otherCardMobile.vue";
import AdCardMobile from "@/components/adCardMobile.vue";
import {safePush} from "@/utils/router.ts";

const systemStore = useSystemStore()
const route = useRoute();
const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: [],
  hotTags: [],
  latestArticles: [],
  hotArticles: [],
  prevNext: {
    prev: null,
    next: null
  }
})
const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
    ElMessage.success(res.msg)
  })
}
const handleLike = () => {
  toggleLike({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_liked = res.data.is_liked
    info.value.detail.like_count = res.data.like_count
    ElMessage.success(res.msg)
  })
}
const goToArticle = (id: number) => {
  console.log(id)
  if (id) {
    safePush({
      path: '/stories/details',
      query: { id }
    })
  }
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})

</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile/>
    <div class="flex-1 auto" style="overflow-x: hidden">
      <div class="contents bg-white">
        <div class="info">
          <div class="title">
            <span>{{info.detail.title}}</span>
          </div>
          <div class="tips flex align-center">
            <div class="author">
              <span>{{info.detail.author}}</span>
            </div>
            <div class="time">
              <span>{{info.detail.publish_date}}</span>
            </div>
            <div class="zan flex align-center">
              <img src="@/assets/images/common/icon_zans.png" alt="">
              <span>{{info.detail.like_count}}</span>
            </div>
          </div>
          <div class="html" v-html="info.detail.content"></div>
          <div class="tools flex align-center justify-center">
            <el-button class="text-white flex-1" :class="{active: info.detail?.is_collected}" @click="handleCollect">
              <img src="@/assets/images/common/icon_star_1.png" alt="">
              <span>收藏 {{info.detail.collect_count}}</span>
            </el-button>
            <el-button class="text-white flex-1" :class="{active: info.detail?.is_liked}" @click="handleLike">
              <img src="@/assets/images/common/icon_zan_1.png" alt="">
              <span>点赞 {{info.detail.like_count}}</span>
            </el-button>
          </div>
          <div class="shares flex flex-column align-center justify-center">
            <div class="name">
              <span>分享内容</span>
            </div>
            <div class="tool flex align-center">
              <div class="button cursor-pointer flex align-center" @click="shareToWechat">
                <img src="@/assets/images/common/icon_wechat.png" alt="">
                <span>微信</span>
              </div>
              <div class="button cursor-pointer flex align-center" @click="shareToWeibo(info.detail.title)">
                <img src="@/assets/images/common/icon_sina.png" alt="">
                <span>微博</span>
              </div>
              <div class="button cursor-pointer flex align-center" @click="shareToQQ(title, image)">
                <img src="@/assets/images/common/icon_tencent.png" alt="">
                <span>QQ</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="copyright">
        <span>版权及免责声明：本网站所有文章除标明原创外，均来自网络。登载本文的目的为传播行业信息，内容仅供参考，如有侵权请联系删除。文章版权归原作者及原出处所有。本网拥有对此声明的最终解释权</span>
      </div>
      <div class="pages flex align-center justify-between">
        <div class="page-card bg-white flex-1" :class="{disabled: !info.prevNext.prev}">
          <div class="name">
            <span>{{info.prevNext.prev ? info.prevNext.prev.title : '没有上一篇'}}</span>
          </div>
          <el-button class="text-white" :disabled="!info.prevNext.prev" @click="goToArticle(info.prevNext.next.id)">上一篇</el-button>
        </div>
        <div class="page-card bg-white flex-1" :class="{disabled: !info.prevNext.next}">
          <div class="name">
            <span>{{info.prevNext.next ? info.prevNext.next.title : '没有下一篇'}}</span>
          </div>
          <el-button class="text-white" :disabled="!info.prevNext.next" @click="goToArticle(info.prevNext.next.id)">下一篇</el-button>
        </div>
      </div>
      <div class="card hidden">
        <otherCardMobile title="热门标签" :icon="getAssetsFile('common/icon_tag.png')" padding="12px 12px">
          <div class="tags flex">
            <div class="tag cursor-pointer" v-for="tag in info.hotTags">#{{tag.name}}</div>
          </div>
        </otherCardMobile>
      </div>
      <div class="card hidden">
        <otherCardMobile title="最新文章" :icon="getAssetsFile('common/icon_book.png')" padding="12px 12px">
          <div class="books">
            <div class="book cursor-pointer row1" v-for="article in info.latestArticles" :key="article.id" @click="goToArticle(article.id)">
              <span>{{article.title}}</span>
            </div>
          </div>
        </otherCardMobile>
      </div>
      <div class="card hidden">
        <otherCardMobile title="热点资讯" :icon="getAssetsFile('common/icon_hot.png')" padding="12px 12px">
          <div class="books">
            <div class="book cursor-pointer row1" v-for="article in info.hotArticles" :key="article.id" @click="goToArticle(article.id)">
              <span>{{article.title}}</span>
            </div>
          </div>
        </otherCardMobile>
      </div>
      <div class="card hidden">
        <adCardMobile />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #F4F6F9;
  height: 100vh;
  .contents{
    margin-top: 5px;
    padding: 15px;
    box-sizing: border-box;
    .info{
      .title{
        font-size: 18px;
        color: #1F1F1F;
        font-weight: 600;
      }
      .tips{
        margin: 12px 0 16px;
        .author{
          color: #3670EE;
          font-size: 12px;
          font-weight: 500;
          background-color: #EBF1FD;
          padding: 2px 10px;
          border-radius: 4px;
        }
        .time{
          color: #191B1F;
          font-size: 14px;
          margin: 0 14px;
        }
        .zan{
          color: #FF6E1B;
          font-size: 14px;
          img{
            width: 20px;
            margin-right: 6px;
          }
        }
      }
      .tools{
        margin: 40px 0 50px;
        .el-button{
          font-weight: 600;
          font-size: 16px;
          background-color: #9DA5AE;
          height: 46px;
          min-height: unset;
          line-height: 1;
          padding: 0;
          border-radius: 8px;
          border: 0;
          &:hover{
            color: #fff;
          }
          &.active{
            background: linear-gradient( 180deg, #39B3E6 0%, #3B78C8 100%);
          }
          img{
            width: 18px;
            height: 18px;
            margin-right: 6px;
          }
        }
      }
      .shares{
        .name{
          font-size:  16px;
        }
        .tool{
          margin-top: 15px;
          .button{
            color: #8A8A8A;
            font-size: 14px;
            padding: 9px 25px;
            box-sizing: border-box;
            background-color: #F3F5F7;
            border-radius: 8px;
            &+.button{
              margin-left: 10px;
            }
            img{
              width: 20px;
              height: 20px;
              margin-right: 6px;
            }
          }
        }
      }
    }
  }
  .copyright{
    margin: 20px 15px 42px;
    color: #8A8A8A;
    font-size: 12px;
  }
  .pages{
    margin: 0 15px 20px;
    .page-card{
      padding: 10px;
      box-sizing: border-box;
      color: #191B1F;
      font-size: 13px;
      font-weight: 500;
      box-shadow: 0 1px 3px 0 rgba(8,48,123,0.06);
      border-radius: 12px;
      &+.page-card{
        margin-left: 13px;
      }
      .el-button{
        background: linear-gradient( 180deg, #39B3E6 0%, #3B78C8 100%);
        border-radius: 8px;
        width: 100%;
        border: 0;
        margin-top: 15px;
        height: 26px;
        &:hover{
          color: #fff;
        }
      }
    }
  }
  .card{
    margin: 20px 15px;
    box-shadow: 0 2px 6px 0 rgba(8,48,123,0.06);
    border-radius: 12px;
    .tags{
      flex-wrap: wrap;
      .tag{
        background-color: #E5ECFE;
        color: $maincolor;
        font-size: 12px;
        padding: 5px 10px;
        border-radius: 19px;
        margin-right: 10px;
        margin-bottom: 16px;
      }
    }
    .books{
      .book{
        background-color: #F9F9FA;
        border-radius: 8px;
        padding: 0 12px;
        box-sizing: border-box;
        height: 40px;
        line-height: 40px;
        color: #191B1F;
        font-size: 14px;
        &+.book{
          margin-top: 12px;
        }
      }
    }
  }
}
</style>