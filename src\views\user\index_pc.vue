<script setup lang="ts">
import {ref, watch, onMounted,onUnmounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import Pay from "@/components/pay.vue";
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import userInfoCard from "@/components/userInfoCard.vue";
import userSliderCard from "@/components/userSliderCard.vue";
import paginate from "@/components/paginate.vue";
import type {OrderItem} from '@/types/user.ts'
import {safePush} from "@/utils/router.ts";
import {cancelOrder, checkPayStatus, myOrders} from '@/api/common.ts'
let timeObj = null
const payRef = ref()
const orderInfo = ref({})
const tabs = ref([
  {name: '全部订单', value: ''},
  {name: '待付款', value: 'pending'},
  {name: '已付款', value: 'paid'},
  {name: '进行中', value: 'in_progress'},
  // {name: '已完成', value: 'completed'},
  {name: '已取消', value: 'cancelled'},
  {name: '已退款', value: 'refunded'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref<OrderItem[]>([])
const total = ref(0)
const getStatusColor = (status: string) => {
  let color = '';
  switch (status) {
    case 'pending':
    case '待付款':
      color = '#26B84C';
      break;
    case 'paid':
    case 'in_progress':
    case '待完成':
      color = '#F1A92E';
      break;
    case 'completed':
    case '已完成':
      color = '#1F1F1F';
      break;
    case 'cancelled':
    case 'refunded':
    case '售后中':
      color = '#E5331B';
      break;
    default:
      color = '#1F1F1F';
      break;
  }
  return color;
}
const handleAfterSales = (item: OrderItem) => {
  safePush('/user/afterSales')
}
// 处理支付
const handlePayment = (item: OrderItem) => {
  orderInfo.value = item
  payRef.value.open()
  timeObj = setInterval(() => {
    checkOrders()
  }, 3000)
}
const handleCancel = (item: OrderItem) => {
  ElMessageBox.confirm('是否确认取消订单?')
      .then(() => {
        cancelOrder({
          order_id: item.id,
          reason: '用户取消订单'
        }).then((res) => {
          ElMessage.success(res.msg)
          getMyOrders()
        })
      })
      .catch(() => {

      })

}
const getMyOrders = () => {
  myOrders({
    status: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value,
  }).then(res => {
    total.value = res.data.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getMyOrders()
}
const checkOrders = () => {
  checkPayStatus({order_id: orderInfo.value.id}).then(res => {
    if (['paid', 'completed'].includes(res.data.status)) {
      // 已付款、已完成
      payRef.value.close()
      clearInterval(timeObj)
      ElMessage.success(res.msg)
      getMyOrders()
    } else if (['cancelled', 'refunded'].includes(res.data.status)) {
      // 已取消、已退款
      payRef.value.close()
      clearInterval(timeObj)
      ElMessage.error(res.msg)
      getMyOrders()
    }
  })
}
const handleClosePop = () => {
  timeObj && clearInterval(timeObj)
}
const handleRefuse = (item: OrderItem) => {
  safePush({
    path: '/user/afterSales',
    query: {
      id: item.id
    }
  })
}
watch(tabCur, (val) => {
  page.value = 1
  list.value = []
  getMyOrders()
}, {
  deep: true
})
onMounted(() => {
  getMyOrders()
})
onUnmounted(()=>{
  timeObj && clearInterval(timeObj)
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="userinfo">
      <div class="container">
        <userInfoCard/>
      </div>
    </div>
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="sliders">
          <userSliderCard/>
        </div>
        <div class="box bg-white flex-1">
          <div class="tabs flex align-center">
            <div class="tab cursor-pointer" :class="{active: tabCur === index}" @click="tabCur = index"
                 v-for="(item, index) in tabs" :key="index">
              <span>{{ item.name }}</span>
            </div>
          </div>
          <div class="items">
            <div class="item flex justify-between" v-for="(item, index) in list" :key="index">
              <div class="img">
                <img :src="item.cover_image" :alt="item.service_title">
              </div>
              <div class="info flex-1 position-relative">
                <div class="title flex align-center justify-between">
                  <div class="name">
                    <span>{{ item.service_title }}</span>
                  </div>
                  <div class="status" :style="{color: getStatusColor(item.status)}">
                    <span>{{ item.status_text }}</span>
                  </div>
                </div>
                <div class="money">
                  <span>¥{{ item.total_amount }}</span>
                </div>
                <div class="tools position-absolute">
                  <template v-if="item.status === 'pending'">
                    <el-button type="danger" @click="handleCancel(item)">取消订单</el-button>
                    <el-button type="success" @click="handlePayment(item)">立即支付</el-button>
                  </template>
                  <template v-else-if="['paid', 'in_progress'].includes(item.status)">
                    <el-button type="danger" @click="handleRefuse(item)">申请退款</el-button>
                  </template>
                  <template v-if="item.status === 'completed'">
                    <el-button @click="handleAfterSales(item)">申请售后</el-button>
                  </template>
                  <template v-if="item.status === 'refunded'">
                    <el-button>取消售后</el-button>
                  </template>
                </div>
              </div>
            </div>
          </div>
          <div class="paginates">
            <paginate :limit="limit" :total="total" @change="changePage"/>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer/>
  </div>
  <Pay ref="payRef" :orderInfo="orderInfo" @close="handleClosePop" />
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;

  .userinfo {
    margin: px2rem(20) 0;
  }

  .boxs {
    .sliders {
      width: px2rem(238);
    }

    .box {
      margin-left: px2rem(20);
      border-radius: px2rem(16);

      .tabs {
        padding: px2rem(24) px2rem(40) px2rem(11);
        box-sizing: border-box;
        border-bottom: px2rem(1) solid #E9E9E9;

        .tab {
          color: #535871;
          font-size: px2rem(18);
          border-bottom: px2rem(3) solid transparent;
          line-height: 1;
          padding-bottom: px2rem(5);

          & + .tab {
            margin-left: px2rem(50);
          }

          &.active {
            color: $maincolor;
            border-color: $maincolor;
          }
        }
      }

      .items {
        padding: px2rem(30) px2rem(40);
        box-sizing: border-box;

        .item {
          background-color: #F4F6F9;
          padding: px2rem(20);
          box-sizing: border-box;
          border-radius: px2rem(8);

          & + .item {
            margin-top: px2rem(20);
          }

          .img {
            width: px2rem(158);
            height: px2rem(100);
            border-radius: px2rem(10);
            margin-right: px2rem(20);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .info {
            .title {
              font-weight: 500;

              .name {
                font-size: px2rem(22);
              }

              .status {
                font-size: px2rem(18);
              }
            }

            .money {
              font-size: px2rem(20);
              color: #525252;
              margin-top: px2rem(12);
            }

            .tools {
              right: 0;
              bottom: 0;

              .el-button {
                border-radius: px2rem(23);
                border: 0;
                font-size: px2rem(16);

                &.el-button--success {
                  background-color: #67C23A;
                  border-color: #67C23A;
                  color: #fff;

                  &:hover {
                    background-color: #85CE61;
                    border-color: #85CE61;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .empty {
    height: px2rem(100);
  }
}
</style>