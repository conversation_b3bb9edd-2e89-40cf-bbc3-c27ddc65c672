<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { expertList } from '@/api/common.ts'
import HeaderMobile from "@/components/headerMobile.vue";
import ExpertCardMobile from "@/components/expertCardMobile.vue";
import paginate from "@/components/paginateMobile.vue";
const limit = ref(10)
const pageInfo = ref({
  title: '',
  subtitle: '',
})
const filters = ref({
  category_id: '',
  channel_id: '',
  keyword: '',
  research_field: ''
})
const page = ref(1)
const total = ref(0)
const list = ref([])
const getExpertList = () => {
  expertList({
    ...filters.value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    pageInfo.value = res.data.pageInfo
    total.value = res.data.pagination.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getExpertList()
}
onMounted(() => {
  getExpertList()
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="items auto flex-1">
      <div class="list">
        <ExpertCardMobile :info="item" v-for="(item, index) in list" :key="index" />
      </div>
    </div>
    <div class="paginates" v-if="total > limit">
      <paginate :limit="limit" :total="total" @change="changePage" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #FDFDFF;
  height: 100vh;
  .items{
    padding: 12px 15px 0;
    box-sizing: border-box;
    .list{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px;
      grid-auto-rows: min-content;
    }
  }
  .paginates{
    margin: 10px 0;
  }
}
</style>