<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Star, StarFilled } from '@element-plus/icons-vue'
import HeaderMobile from "@/components/headerMobile.vue";
import { contentDetail, toggleCollect } from '@/api/common.ts'
import {ElMessage} from "element-plus";
import { useRoute } from 'vue-router'
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import ShareCardMobile from "@/components/shareCardMobile.vue";
import OtherCardMobile from "@/components/otherCardMobile.vue";
import ServiceCardMobile from "@/components/serviceCardMobile.vue";
import ExpertCardMobile from "@/components/expertCardMobile.vue";
import AboutCardMobile from "@/components/aboutCardMobile.vue";
import JournalCardMobile from "@/components/journalCardMobile.vue";
import { useSystemStore } from '@/store/system.ts'
const systemStore = useSystemStore()
const route = useRoute();
const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: []
})
const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
    ElMessage.success(res.msg)
  })
}
const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="flex-1 auto" style="overflow-x: hidden">
      <div class="card bg-white">
        <div class="box bg-white flex justify-between">
          <div class="left hidden">
            <el-avatar :size="60" :src="isHttpOrHttps(info.detail.avatar_url)" />
          </div>
          <div class="right flex-1">
            <div class="title flex align-center">
              <div class="name">
                <span>{{info.detail.title}}</span>
              </div>
              <div class="tags">
                <span>{{info.detail.expert_number ?? '-'}}</span>
              </div>
            </div>
            <div class="desc">
              <span>{{info.detail.summary}}</span>
            </div>
            <div class="tag-list flex">
              <div class="tag" v-for="(item, index) in info.detail.research_area?.split(',')">
                <span>{{item}}</span>
              </div>
            </div>
            <div style="margin-top: 4px">
              <ShareCardMobile :title="info.detail.title" :image="isHttpOrHttps(info.detail.avatar_url)" style="margin-top: 4px">
                <div class="tool">
                  <el-button type="primary" @click="jumpOutWeb(systemStore.kefu_url)">立即咨询</el-button>
                  <el-button color="#EEAF3D" :icon="info.detail?.is_collected ? StarFilled : Star" @click="handleCollect">收藏</el-button>
                </div>
              </ShareCardMobile>
            </div>
          </div>
        </div>
      </div>
      <div class="card bg-white">
        <div class="info">
          <template v-if="info.detail.academic_achievements">
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>学术成果</span>
            </div>
            <div class="values">
              <span>{{info.detail.academic_achievements}}</span>
            </div>
          </template>
          <template v-if="info.detail.tutoring_experience">
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>辅导经验</span>
            </div>
            <div class="values">
              <span>{{info.detail.tutoring_experience}}</span>
            </div>
          </template>
          <template v-if="info.detail.expertise_skills">
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>擅长技能</span>
            </div>
            <div class="values">
              <span>{{info.detail.expertise_skills}}</span>
            </div>
          </template>
          <template v-if="info.detail.service_types">
            <div class="name-bar flex align-center">
              <img src="@/assets/images/common/icon_item.png" alt="">
              <span>服务类型</span>
            </div>
            <div class="values">
              <span>{{info.detail.service_types}}</span>
            </div>
          </template>
        </div>
      </div>
      <div class="card">
        <OtherCardMobile title="相关推荐" :icon="getAssetsFile('common/icon_zan.png')" more-url="/expert">
          <div class="services">
            <ExpertCardMobile :info="item" v-for="(item, index) in info.recommended" :key="index" />
          </div>
          <div class="about" v-if="info.related.experts?.length">
            <aboutCardMobile title="热门服务" :icon="getAssetsFile('common/icon_service.png')" moreUrl="/expert">
              <div class="items">
                <ServiceCardMobile :info="item" v-for="(item, index) in info.related.experts" :key="index" />
              </div>
            </aboutCardMobile>
          </div>
          <div class="about" v-if="info.related.journals?.length">
            <aboutCardMobile title="相关书籍" :icon="getAssetsFile('common/icon_books.png')" moreUrl="/journal">
              <div class="items">
                <JournalCardMobile :info="item" v-for="(item, index) in info.related.journals" :key="index" />
              </div>
            </aboutCardMobile>
          </div>
        </OtherCardMobile>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #F4F6F9;
  height: 100vh;
  .card {
    margin-top: 5px;
    .box{
      padding: 20px 15px;
      box-sizing:  border-box;
      .left{
        width: 60px;
        height: 60px;
        border-radius: 50%;
        img{
          width: 100%;
        }
      }
      .right{
        margin-left: 16px;
        .title{
          .name{
            font-size: 18px;
            color: #2D2B32;
            line-height: 1;
            font-weight: 600;
          }
          .tags{
            background: linear-gradient( 90deg, #F8F0DF 0%, #EFE3C8 100%);
            margin-left: 10px;
            border-radius: 4px;
            padding: 2px 10px;
            box-sizing: border-box;
            color: #452111;
            font-size: 12px;
            font-weight: 500;
          }
        }
        .desc{
          margin: 6px 0;
          color: #535871;
          font-size: 14px;
          line-height: 22px;
        }
        .tag-list{
          flex-wrap: wrap;
          .tag{
            border: px2rem(1) solid #CCCCCC;
            border-radius: 4px;
            color: #434864;
            font-size:  12px;
            padding: 2px 6px;
            margin-bottom: 8px;
            &+.tag{
              margin-left: 6px;
            }
          }
        }
        .tool{
          .el-button{
            font-size: 14px;
            width: 98px;
            height: 32px;
            border-radius: 27px;
            &:last-child{
              background-color: transparent;
              color: #EEAF3D;
            }
          }
        }
      }
    }
    .info{
      padding: 20px 15px;
      box-sizing:  border-box;
      .name-bar{
        color: #191B1F;
        font-weight: 600;
        font-size: 16px;
        padding-bottom: 10px;
        box-sizing: border-box;
        img{
          width: 23px;
          height: 16px;
          margin-right: 10px;
        }
      }
      .values{
        color: #525252;
        font-size: 14px;
        line-height: 25px;
        margin-bottom: 43px;
      }
    }
    .services{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px 11px;
      grid-auto-rows: min-content;
    }
    .about{
      margin-top: 30px;
      .items{
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 12px 11px;
        grid-auto-rows: min-content;
      }
    }
  }
}
</style>