<script setup lang="ts">
import {ref, onMounted, watch} from 'vue'
import {journalList, getSubjectOptions} from '@/api/common.ts'
import HeaderMobile from "@/components/headerMobile.vue";
import JournalCardMobile from "@/components/journalCardMobile.vue";
import paginate from "@/components/paginateMobile.vue";
import {useRoute, useRouter} from 'vue-router'
import {Close} from '@element-plus/icons-vue'
import {useSystemStore} from '@/store/system'
import {getAssetsFile, isHttpOrHttps} from "@/utils/utils.ts";
import HeaderTitle from "@/components/headerTitle.vue";
import {safePush} from "@/utils/router.ts";

const systemStore = useSystemStore()
const route = useRoute();
const router = useRouter();
const containerRef = ref<HTMLElement | null>(null)
const show = ref(false)
const limit = ref(10)
const pageInfo = ref({
  title: '',
  subtitle: '',
  totalCount: 0
})
const filters = ref({
  big_type: '',
  journal_datatype: '',
  journal_jcr: '',
  journal_type: '',
  journal_zky: '',
  keyword: '',
  small_type: '',
  impact_factor: ['', '']
})
const tempFilters = ref({
  big_type: '',
  journal_datatype: '',
  journal_jcr: '',
  journal_type: '',
  journal_zky: '',
  keyword: '',
  small_type: '',
  impact_factor: ['', '']
})
const optionCur = ref(0)
const options = ref([
  {
    name: '大类学科',
    field: 'big_type',
    type: 'select',
    options: []
  },
  {
    name: '小类学科',
    field: 'small_type',
    type: 'select',
    options: []
  },
  {
    name: '期刊类型',
    field: 'journal_type',
    type: 'select',
    options: []
  },
  {
    name: '数据库',
    field: 'journal_datatype',
    type: 'select',
    options: []
  },
  {
    name: '中科院',
    field: 'journal_zky',
    type: 'select',
    options: []
  },
  {
    name: 'JCR',
    field: 'journal_jcr',
    type: 'select',
    options: []
  },
  {
    name: '影响因子',
    field: 'impact_factor',
    type: 'range',
    placeholder: ['大于因子', '小于因子']
  },
  {
    name: '关键词',
    field: 'keyword',
    type: 'input',
    placeholder: '支持模糊检索，多关键词组合',
  }
]);
const total = ref(0)
const page = ref(1)
const list = ref([])
const getJournalList = () => {
  journalList({
    ...filters.value,
    page: page.value,
    limit: limit.value,
  }).then(res => {
    pageInfo.value = res.data.pageInfo
    total.value = res.data.pagination.total
    list.value = res.data.list
    for (let i = 0; i < options.value.length; i++) {
      if (res.data.options[options.value[i].field]) {
        options.value[i].options = res.data.options[options.value[i].field]
      }
    }
  })
}
const changePage = (e: number) => {
  page.value = e
  getJournalList()
}
const handleShowFilter = () => {
  for (let i in tempFilters.value) {
    tempFilters.value[i] = filters.value[i]
  }
  show.value = true
}
const handleFilterConfirm = () => {
  for (let i in tempFilters.value) {
    filters.value[i] = tempFilters.value[i]
  }
  show.value = false
  page.value = 1
  getJournalList()
}
const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
const handleQK = (e: any) => {
  let path = ''
  switch(e.content_type){
    case 'SERVICE':
      path = "/service/details"
      break;
    case 'EXPERT':
      path = "/expert/details"
      break;
  }
  safePush({
    path: path,
    query: {
      id: e.remoteValue
    }
  })
}
onMounted(() => {
  getJournalList()
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile/>
    <div class="parts flex align-center">
      <div class="part cursor-pointer" v-for="(item, index) in systemStore.qk_image" :key="index" @click="handleQK(item)">
        <img class="flex" :src="isHttpOrHttps(item.image)" alt="">
      </div>
    </div>
    <div class="filters flex align-center justify-between">
      <div class="values flex align-center" v-if="filters.service_scenario || filters.service_type">
        <div class="value" v-for="(item, index) in options" :key="index">
          <span v-if="filters[item.field]">{{
              item.data.find(child => child.key === filters[item.field])?.label
            }}</span>
        </div>
      </div>
      <div class="name" v-else>筛选</div>
      <div class="icon" @click="handleShowFilter">
        <img class="flex" src="@/assets/images/common_mobile/icon_filter.png" alt="">
      </div>
    </div>
    <div class="items auto flex-1">
      <div class="list">
        <JournalCardMobile :info="item" v-for="(item, index) in list" :key="index"/>
      </div>
    </div>
    <div class="paginates" v-if="total > limit">
      <paginate :limit="limit" :total="total" @change="changePage"/>
    </div>
  </div>
  <el-drawer v-model="show" :show-close="false" size="100%" class="journal-filters-popup">
    <template #header="{ close }">
      <HeaderTitle @click="close"/>
    </template>
    <el-anchor :container="containerRef" class="filter flex justify-between flex-1 hidden" @click="handleClick">
      <div class="slide auto">
        <el-anchor-link class="option text-center" :class="{active: optionCur==index}" v-for="(item, index) in options"
             :key="index" :href="`#${item.name}`" @click="optionCur=index">
          <span>{{ item.name }}</span>
        </el-anchor-link>
      </div>
      <div ref="containerRef" class="box bg-white flex-1 auto">
        <div class="item" :id="item.name" v-for="(item, index) in options" :key="index">
          <div class="title">
            <span>{{ item.name }}</span>
          </div>
          <div :class="item.type">
            <template v-if="item.type === 'select'">
              <div class="child text-center" :class="{active: tempFilters[item.field] == item1.key}"
                   v-for="(item1, index1) in item.options" :key="index1" @click="tempFilters[item.field] = item1.key">
                <span>{{ item1.label }}</span>
              </div>
            </template>
            <template v-else-if="item.type === 'range'">
              <div class="flex align-center justify-between">
                <el-input :placeholder="item.placeholder[0]" v-model="tempFilters[item.field][0]" clearable></el-input>
                <div class="ml-1 mr-1">
                  <span>-</span>
                </div>
                <el-input :placeholder="item.placeholder[1]" v-model="tempFilters[item.field][1]" clearable></el-input>
              </div>
            </template>
            <template v-else-if="item.type === 'input'">
              <el-input :placeholder="item.placeholder" v-model="tempFilters[item.field]" clearable></el-input>
            </template>
          </div>
        </div>
      </div>
    </el-anchor>
    <template #footer>
      <div class="buttons flex align-center justify-between">
        <div class="button cancel flex-1 text-center" @click="show = false">取消</div>
        <div class="button confirm flex-1 text-center" @click="handleFilterConfirm">确认</div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped lang="scss">
.page {
  background: #FDFDFF;
  height: 100vh;

  .parts {
    margin: 12px 15px 0;
    overflow-x: auto;

    .part {
      & + .part {
        margin-left: 8px;
      }

      img {
        width: 76px;
      }
    }
  }

  .filters {
    margin: 18px 0 14px 15px;

    .name {
      font-size: 12px;
      color: #535871;
    }

    .values {
      .value {
        font-size: 12px;

        & + .value {
          margin-left: 12px;
        }
      }
    }

    .icon {
      width: 51px;
      height: 17px;

      img {
        width: 100%;
      }
    }
  }

  .items {
    padding: 0 15px;
    box-sizing: border-box;

    .list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px 11px;
      grid-auto-rows: min-content;
    }
  }

  .paginates {
    margin: 10px 0;
  }
}
</style>