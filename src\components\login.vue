<script setup lang="ts">
import { ref, onUnmounted, computed, onMounted, watch } from 'vue'
import {getAssetsFile} from "@/utils/utils.ts";
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Loading, Link } from '@element-plus/icons-vue'
import { sendSms, checkIn } from '@/api/login.ts'
import { oauthList } from '@/api/oauth.ts'
import { useUserInfoStore } from '@/store/userInfo.ts'
import { useSystemStore } from '@/store/system.ts'
import { safePush } from '@/utils/router'
import { onLogin, isWeixinBrowser, isMobile } from '@/utils/oauth.ts'
const userInfoStore = useUserInfoStore()
const systemStore = useSystemStore()
const visible = ref(false)
const loginTypes = ref([
  {id: 0, name: '微信登录', code: 'wechat', title: '微信扫码注册登录', icon: getAssetsFile('common/icon_wx.png'), iconHover: getAssetsFile('common/icon_wx_active.png'), oauthName: 'wechat_scan'},
  {id: 1, name: '手机号登录', code: 'mobile', title: '手机号注册登录', icon: getAssetsFile('common/icon_mobile.png'), iconHover: getAssetsFile('common/icon_mobile_active.png'), oauthName: ''},
  {id: 2, name: 'QQ登录', code: 'qq', title: 'QQ扫码注册登录', icon: getAssetsFile('common/icon_qq.png'), iconHover: getAssetsFile('common/icon_qq_active.png'), oauthName: 'qq'},
])
const loginType = ref(0)
const isAgree = ref(true)
const hoveredIndex = ref(-1)
const formRef = ref()
const countdown = ref(0)
const timer = ref<number | null>(null)
const availableOauthNames = ref<string[]>([])
const popupKey = ref('')
const isLoadingOauth = ref(false)
const qrcodeError = ref(false)
const dialogVisible = ref(false)
const formData = ref({
  mobile: '',
  captcha: ''
})
const rules = ref<FormRules>({
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号错误', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码错误', trigger: 'blur' }
  ]
})
const loginOtherTypes = computed(() => {
  return loginTypes.value.filter((_, index) => index !== loginType.value)
})

// 检查登录类型是否可用
const isLoginTypeAvailable = (loginTypeItem: any) => {
  if (loginTypeItem.oauthName) {
    return availableOauthNames.value.includes(loginTypeItem.oauthName)
  }
  return true // 非OAuth登录类型默认可用
}
// 监听父组件传递的显示状态
const show = async () => {
  visible.value = true
  // 禁止背景滚动
  document.body.style.overflow = 'hidden'
  
  // 确保OAuth平台列表已加载
  if (availableOauthNames.value.length === 0 && !isLoadingOauth.value) {
    console.log('重新获取OAuth平台列表',loginType.value)
    await getOauthList()
    if (loginType.value === 0) { // 微信登录
      console.log('切换到微信登录，准备生成二维码')
      // 检查是否支持微信扫码登录
      if (availableOauthNames.value.includes('wechat_scan')) {
        console.log('支持微信扫码登录，生成二维码')
        // 延迟一点生成，确保DOM已更新
        setTimeout(() => {
          generateWechatQrcode()
        }, 200)
      } else {
        console.warn('不支持微信扫码登录')
      }
    }
  } else {
    if (loginType.value === 0) { // 微信登录
      console.log('切换到微信登录，准备生成二维码')
      // 检查是否支持微信扫码登录
      if (availableOauthNames.value.includes('wechat_scan')) {
        console.log('支持微信扫码登录，生成二维码')
        // 延迟一点生成，确保DOM已更新
        setTimeout(() => {
          generateWechatQrcode()
        }, 200)
      } else {
        console.warn('不支持微信扫码登录')
      }
    }
  }
  
  console.log('登录弹窗显示，可用OAuth平台:', availableOauthNames.value)


}

const hide = () => {
  visible.value = false
  // 恢复背景滚动
  document.body.style.overflow = ''
  formData.value = {
    mobile: '',
    captcha: ''
  }
}

// 处理遮罩点击
const handleMaskClick = (e: MouseEvent) => {
  // 确保点击的是遮罩层本身，而不是其子元素
  if (e.target === e.currentTarget) {
    hide()
  }
}
const handleLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  try {
    const valid = await formEl.validate()
    if (valid) {
      if (!isAgree.value) {
        ElMessage.error('请阅读并同意《用户协议》、《版权声明》、《隐私政策》')
        return
      }
      const res = await checkIn({
        tab: 'mobile_login',
        keep: 1,
        mobile: formData.value.mobile,
        captcha: formData.value.captcha
      })
      ElMessage.success(res.msg)
      userInfoStore.setUserInfo(res.data.userInfo)
      userInfoStore.setIsLogin(true)
      hide()
      setTimeout(() => {
        safePush('/user')
      }, 100);
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const handleGetCode = () => {
  if (countdown.value > 0) return
  if (!formData.value.mobile) {
    ElMessage.warning('请输入手机号')
    return
  }
  sendSms({
    template_code: 'user_login',
    mobile: formData.value.mobile,
  }).then((res: any) => {
    console.log(res)
    countdown.value = 60
    timer.value = window.setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        timer.value && clearInterval(timer.value)
      }
    }, 1000)
    ElMessage.success(res.msg)
  })
}
const handleChangeLoginType = (e: any) => {
  console.log('切换登录类型:', e)
  console.log('可用OAuth平台:', availableOauthNames.value)
  
  // 检查是否是OAuth登录
  if (e.oauthName && availableOauthNames.value.includes(e.oauthName)) {
    if (!isAgree.value) {
      return ElMessage.error('请阅读并同意《用户协议》、《版权声明》、《隐私政策》')
    }
    
    // 微信登录需要特殊处理
    if (e.oauthName === 'wechat_scan') {
      // 如果是微信浏览器，使用微信公众号登录
      if (isWeixinBrowser()) {
        console.log('微信浏览器环境，使用公众号登录')
        onLogin('wechat_mp')
      } else {
        // 否则显示扫码界面
        console.log('非微信浏览器，显示扫码界面')
        loginType.value = e.id
        // 延迟生成二维码，确保界面已切换
        setTimeout(() => {
          generateWechatQrcode()
        }, 100)
      }
    } else {
      // 其他OAuth登录直接跳转
      console.log('其他OAuth登录:', e.oauthName)
      onLogin(e.oauthName)
    }
  } else if (e.code === 'mobile') {
    // 手机号登录
    console.log('切换到手机号登录')
    loginType.value = e.id
  } else {
    // 其他登录方式暂时不可用
    console.log('登录方式不可用:', e)
    ElMessage.warning(`${e.name}暂不可用`)
  }
}


// 动态加载微信JS SDK
const loadWeChatSDK = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 检查是否已加载
    if ((window as any).WxLogin) {
      console.log('✅ 微信JS SDK已存在')
      resolve()
      return
    }
    
    console.log('🔄 开始加载微信JS SDK...')
    
    // 动态创建script标签
    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js'
    script.async = true
    
    // 设置超时时间
    const timeout = setTimeout(() => {
      console.error('❌ 微信JS SDK加载超时')
      reject(new Error('微信JS SDK加载超时，请检查网络连接'))
    }, 10000) // 10秒超时
    
    script.onload = () => {
      clearTimeout(timeout)
      console.log('✅ 微信JS SDK加载成功')
      // 等待一小会确保SDK完全初始化
      setTimeout(() => {
        resolve()
      }, 100)
    }
    
    script.onerror = (error: any) => {
      clearTimeout(timeout)
      console.error('❌ 微信JS SDK加载失败:', error)
      reject(new Error('微信JS SDK加载失败，请检查网络连接'))
    }
    
    document.head.appendChild(script)
  })
}

// 生成微信扫码二维码
const generateWechatQrcode = async () => {
  try {
    console.log('🚀 开始生成微信二维码...')
    
    // 重置错误状态
    qrcodeError.value = false
    
    // 动态加载微信JS SDK
    await loadWeChatSDK()

    // 检查容器是否存在
    const container = document.getElementById('wechat_qrcode_container')
    if (!container) {
      throw new Error('微信二维码容器不存在')
    }
    
    // 清空容器并显示加载提示
    container.innerHTML = `
      <div style="padding: 40px; text-align: center; color: #666;">
        <div style="font-size: 14px; margin-bottom: 10px;">🔄 正在加载微信二维码...</div>
        <div style="font-size: 12px; opacity: 0.7;">请稍等片刻</div>
      </div>
    `
    
    // 使用微信官方JS SDK生成内嵌二维码
    const obj = new (window as any).WxLogin({
      self_redirect: false,
      id: "wechat_qrcode_container", // 二维码容器ID
      appid: "wx87183cd153ad4464", // 从后端配置获取
      scope: "snsapi_login", 
      redirect_uri: encodeURIComponent("https://www.yanzhiyoushu.cn/user"),
      state: `wechat_scan__${Date.now()}__login`,
      style: "black",
      href: "",
      stylelite: 1, // 使用新版样式
    });
    
    // 设置二维码加载超时
    const qrcodeTimeout = setTimeout(() => {
      console.warn('⏰ 微信二维码加载超时，显示备用方案')
      const container = document.getElementById('wechat_qrcode_container')
      if (container && container.innerHTML.includes('正在连接')) {
        container.innerHTML = `
          <div style="padding: 20px; text-align: center; color: #666;">
            <p>微信二维码加载较慢</p>
            <p style="font-size: 12px; margin: 10px 0;">请稍等片刻或尝试备用方案</p>
            <button onclick="location.reload()" style="padding: 8px 16px; margin: 5px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">
              刷新页面
            </button>
          </div>
        `
      }
    }, 15000) // 15秒后显示备用方案
    
    // 监听二维码加载完成
    const checkQrcode = setInterval(() => {
      const container = document.getElementById('wechat_qrcode_container')
      if (container) {
        const iframe = container.querySelector('iframe')
        if (iframe) {
          clearInterval(checkQrcode)
          clearTimeout(qrcodeTimeout)
          console.log('✅ 微信扫码二维码加载成功')
        }
      }
    }, 500) // 每500ms检查一次
    
    // 5秒后如果还没加载完成，清除检查
    setTimeout(() => {
      clearInterval(checkQrcode)
    }, 5000);
    
    console.log('✅ 微信二维码实例创建成功')
  } catch (error: any) {
    console.error('❌ 生成微信二维码失败:', error)
    qrcodeError.value = true
    
    // 显示详细错误信息
    const errorMessage = error?.message || '生成二维码失败'
    ElMessage.error(`微信登录失败: ${errorMessage}`)
    
    // 在容器中显示错误信息
    const container = document.getElementById('wechat_qrcode_container')
    if (container) {
      container.innerHTML = `
        <div style="padding: 20px; text-align: center; color: #666;">
          <p>微信二维码加载失败</p>
          <p style="font-size: 12px; margin: 10px 0;">${errorMessage}</p>
          <button onclick="location.reload()" style="padding: 8px 16px; border: 1px solid #ddd; background: #f5f5f5; border-radius: 4px; cursor: pointer;">
            重新加载页面
          </button>
        </div>
      `
    }
  }
}

// 备用微信登录方案
const openLoginFallback = () => {
  const baseUrl = import.meta.env.VITE_API_URL
  const authUrl = `${baseUrl}/api/OAuthLogin/index?name=wechat_scan&type=login`
  window.open(authUrl, '_blank')
}


  // 获取可用的OAuth平台列表
const getOauthList = async () => {
  if (isLoadingOauth.value) return

  isLoadingOauth.value = true
  try {
    const response: any = await oauthList()
    if (response && response.code === 1) {
      availableOauthNames.value = response.data.oauthNames || []
    }
  } catch (error) {
    console.error('获取OAuth平台列表失败:', error)
    // 如果失败，设置默认的OAuth平台（基于后端系统的配置）
    availableOauthNames.value = ['qq', 'wechat_scan', 'wechat_mp', 'wechat_mini_program']
  } finally {
    isLoadingOauth.value = false
  }
}

const showPop = (e: string) => {
  popupKey.value = e
  dialogVisible.value = true
}

// 组件卸载时确保恢复滚动和清除定时器
onUnmounted(() => {
  document.body.style.overflow = ''
  timer.value && clearInterval(timer.value)
})

defineExpose({
  show,
  hide
})
</script>

<template>
  <Teleport to="body">
    <div v-show="visible" class="login-mask position-fixed" @click="handleMaskClick">
      <div class="loginpopup position-absolute flex justify-between">
        <div class="login">
          <div class="logo">
            <el-image :src="getAssetsFile('common/login_logo.png')"></el-image>
          </div>
          <div class="name text-white">
            <span>欢迎使用 研知有术</span>
          </div>
          <div class="slogin text-white">
            <span>辅助标语Slogan</span>
          </div>
          <div class="book">
            <el-image :src="getAssetsFile('common/login_book.png')"></el-image>
          </div>
        </div>
        <div class="form bg-white flex flex-column align-center text-center position-relative">
          <div class="title">
            <span>{{loginTypes[loginType].title}}</span>
          </div>
          <div class="welcome">
            <span>欢迎来到研知有术</span>
          </div>
          <div class="box" :class="loginTypes[loginType].code">
            <template v-if="loginType === 0">
              <div class="qrcode">
                <!-- 微信官方二维码容器 -->
                <div id="wechat_qrcode_container" style="width: 160px; height: 160px;"></div>
              </div>
            </template>
            <template v-else-if="loginType === 1">
              <div class="phonebox">
                <el-form ref="formRef" :model="formData" :rules="rules">
                  <el-form-item prop="mobile">
                    <el-input v-model="formData.mobile" placeholder="请输入手机号" maxlength="11"></el-input>
                  </el-form-item>
                  <el-form-item prop="captcha">
                    <el-input v-model="formData.captcha" placeholder="请输入验证码" maxlength="6">
                      <template #suffix>
                        <div @click.stop.prevent>
                          <el-link type="primary" underline="never" :class="{codeing: countdown}" @click.stop.prevent="handleGetCode">
                            {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
                          </el-link>
                        </div>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-form>
                <div class="confirm">
                  <el-button class="text-white" @click="handleLogin(formRef)">登录</el-button>
                </div>
              </div>
            </template>
          </div>
          <div class="logintypes flex align-center justify-center" :class="loginTypes[loginType].code">
            <div class="logintype flex align-center cursor-pointer" 
                 v-for="(item, index) in loginOtherTypes" 
                 :key="index"
                 @mouseenter="hoveredIndex = index"
                 @mouseleave="hoveredIndex = -1" 
                 @click="handleChangeLoginType(item)"
                 :class="{ 'disabled': !isLoginTypeAvailable(item) }">
              <el-image :src="hoveredIndex === index ? item.iconHover : item.icon"></el-image>
              <span>{{item.name}}</span>
            </div>
          </div>
          <div class="tips position-absolute flex align-center justify-center">
            <el-checkbox v-model="isAgree">同意并阅读</el-checkbox>
            <el-link underline="never" @click="showPop('userAgreement')">《用户协议》</el-link>
            <span>、</span>
            <el-link underline="never" @click="showPop('copyrightNotice')">《版权声明》</el-link>
            <span>、</span>
            <el-link underline="never" @click="showPop('privacyPolicy')">《隐私政策》</el-link>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
  <el-dialog
      v-model="dialogVisible"
      :title="popupKey === 'userAgreement' ? '用户协议' : popupKey === 'copyrightNotice' ? '版权声明' : popupKey === 'privacyPolicy' ? '隐私政策' : '' "
      width="500"
      class="loginTexts"
      :show-close="false"
  >
    <div class="content">
      <template v-if="popupKey === 'userAgreement'">
        <div v-html="systemStore.yhxy"></div>
      </template>
      <template v-else-if="popupKey === 'copyrightNotice'">
        <div v-html="systemStore.bqsm"></div>
      </template>
      <template v-else-if="popupKey === 'privacyPolicy'">
        <div v-html="systemStore.yszc"></div>
      </template>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
  .login-mask {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    .loginpopup {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: px2rem(800);
      height: px2rem(470);
      border-radius: px2rem(24);
      z-index: 1000;
      background: linear-gradient( 160deg, #39B3E6 0%, $maincolor 100%);
      .login{
        padding: px2rem(46) px2rem(40);
        box-sizing: border-box;
        .logo{
          .el-image{
            width: px2rem(100);
          }
        }
        .name{
          font-size: px2rem(26);
          font-weight: bold;
          margin: px2rem(15) 0;
        }
        .slogin{
          font-size: px2rem(18);
        }
        .book{
          margin: px2rem(33) 0 0 px2rem(10);
          .el-image{
            width: px2rem(200);
          }
        }
      }
      .form{
        border-radius: px2rem(24);
        width: px2rem(500);
        height: 100%;
        .title{
          padding: px2rem(60) 0 px2rem(6);
          box-sizing: border-box;
          font-size: px2rem(20);
          font-weight: 500;
          color: #191B1F;
        }
        .welcome{
          font-size: px2rem(14);
          color: #525252;
          font-weight: 400;
        }
        .box{
          margin-top: px2rem(20);
          &.wechat{
            margin-bottom: px2rem(24);
          }
          .qrcode{
            width: px2rem(160);
            height: px2rem(160);
            border: 1px solid #ebebeb;
          }
          .phonebox{
            :deep(.el-form){
              width: px2rem(310);
              .el-form-item{
                margin-bottom: px2rem(32);
                .el-form-item__content{
                  line-height: px2rem(46);
                  .el-input{
                    line-height: px2rem(46);
                    .el-input__wrapper{
                      border-radius: px2rem(8);
                      box-shadow: 0 0 0 1px #E1E3E8 inset;
                      &.is-focus{
                        box-shadow: 0 0 0 1px $maincolor inset;
                      }
                      .el-input__inner{
                        height: px2rem(46);
                        line-height: px2rem(48);
                        font-size: px2rem(14);
                      }
                      .el-input__suffix{
                        .el-input__suffix-inner{
                          .el-link{
                            color: $maincolor;
                            font-size: px2rem(16);
                            &.codeing{
                              color: #8E94A1;
                            }
                            .el-link__inner{
                              &:hover{
                                color: unset;
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                  .el-form-item__error{
                    padding: px2rem(2) 0 0 px2rem(16);
                    font-size: px2rem(14);
                    color: #E5331B;
                  }
                }
              }
            }
            .confirm{
              .el-button{
                width: 100%;
                height: px2rem(48);
                background: linear-gradient( 180deg, #39B3E6 0%, $maincolor 100%);
                border-radius: px2rem(8);
                font-size: px2rem(16);
                font-weight: 500;
                &:hover {
                  color: $whitecolor;
                }
              }
            }
          }

        }
        .logintypes{
          margin: px2rem(26) 0;
          &.wechat{
            margin-bottom: px2rem(40);
          }
          .logintype{
            font-size: px2rem(14);
            color: #535871;
            &+.logintype{
              margin-left: px2rem(58);
            }
            .el-image{
              width: px2rem(36);
              height: px2rem(36);
              border-radius: 50%;
              margin-right: px2rem(10);
            }
            &.disabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none;
            }
          }
        }
        .tips{
          width: 100%;
          height: px2rem(50);
          background: #FAFAFC;
          border-radius: 0 0 px2rem(24) px2rem(24);
          bottom: 0;
          left: 0;
          :deep(.el-checkbox){
            .el-checkbox__input{
              &.is-checked{
                .el-checkbox__inner{
                  background-color: #69CDBA;
                  border-color: #69CDBA;
                }
              }
              .el-checkbox__inner{
                width: px2rem(16);
                height: px2rem(16);
              }
            }
            .el-checkbox__label{
              color: #7280A7;
              font-size: px2rem(14);
            }
          }
          :deep(.el-link){
            color: #191B1F;
            font-size: px2rem(14);
          }
        }
      }
    }
  }
</style>