<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import { backPage } from "@/utils/router.ts";
import paginateMobile from "@/components/paginateMobile.vue";
import { myConsultations } from '@/api/common.ts'

// 定义咨询项类型
interface ConsultationItem {
  id: number
  name: string
  tel: string
  email: string
  content_id: number
  content_title: string
  cover_image: string
  price: number
  specialization: string
  status: string
  status_text: string
  create_time: string
  remark?: string  // 回复内容
}

const tabs = ref([
  {name: '全部', value: ''},
  {name: '待处理', value: '0'},
  {name: '已完成', value: '1'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref<ConsultationItem[]>([])
const total = ref(0)

// 详情弹窗相关
const dialogDetailVisible = ref(false)
const currentItem = ref<ConsultationItem | null>(null)

const getMyConsultations = () => {
  myConsultations({
    status: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.total
    list.value = res.data.list
  })
}

const changePage = (e: number) => {
  page.value = e
  getMyConsultations()
}

// 查看详情
const viewDetail = (item: ConsultationItem) => {
  currentItem.value = item
  dialogDetailVisible.value = true
}

const getStatusColor = (status: string) => {
  switch (status) {
    case '0':
      return '#F56C6C'  // 待处理
    case '1':
      return '#67C23A'  // 已完成
    default:
      return '#409EFF'
  }
}

watch(tabCur, () => {
  page.value = 1
  list.value = []
  getMyConsultations()
}, {
  deep: true
})

onMounted(() => {
  getMyConsultations()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 flex flex-column hidden">
      <div class="tabs bg-white flex align-center justify-between">
        <div class="tab" :class="{active: tabCur === index}" v-for="(item, index) in tabs" :key="index" @click="tabCur = index">
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="list flex-1 flex flex-column hidden">
        <div class="items flex-1 auto">
          <div class="item bg-white flex" v-for="(item, index) in list" :key="index">
            <div class="img">
              <img :src="item.cover_image" :alt="item.content_title">
            </div>
            <div class="info flex-1">
              <div class="title flex align-center justify-between">
                <div class="name">
                  <span>{{ item.content_title }}</span>
                </div>
                <div class="status" :style="{color: getStatusColor(item.status)}">
                  <span>{{ item.status_text }}</span>
                </div>
              </div>
              <div class="contact-info">
                <div class="contact-item">
                  <span class="label">联系人：</span>
                  <span>{{ item.name }}</span>
                  <span class="separator">|</span>
                  <span class="label">电话：</span>
                  <span>{{ item.tel }}</span>
                </div>
                <div class="contact-item">
                  <span class="label">时间：</span>
                  <span>{{ item.create_time }}</span>
                </div>
              </div>
              <div class="tools">
                <template v-if="item.status === '0'">
                  <span @click="viewDetail(item)">查看详情</span>
                </template>
                <template v-if="item.status === '1'">
                  <span @click="viewDetail(item)">查看回复</span>
                </template>
              </div>
            </div>
          </div>
          <div class="empty-state" v-if="list.length === 0">
            <div class="empty-text">暂无咨询记录</div>
          </div>
        </div>
        <div class="paginates" v-if="total > 0">
          <paginateMobile :limit="limit" :total="total" @change="changePage" />
        </div>
      </div>
    </div>
  </div>

  <!-- 详情抽屉 -->
  <el-drawer
    v-model="dialogDetailVisible"
    :show-close="false"
    size="100%"
    class="consultation-detail-drawer"
    direction="btt"
  >
    <template #header="{ close }">
      <HeaderTitle @click="close"></HeaderTitle>
    </template>
    <div class="detail-content" v-if="currentItem">
      <div class="detail-header">
        <div class="title">咨询详情</div>
      </div>
      <div class="detail-body">
        <div class="detail-item">
          <div class="detail-label">咨询内容</div>
          <div class="detail-value">{{ currentItem.content_title }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">封面图片</div>
          <div class="detail-value">
            <img :src="currentItem.cover_image" :alt="currentItem.content_title" class="cover-image">
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">联系人</div>
          <div class="detail-value">{{ currentItem.name }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">联系电话</div>
          <div class="detail-value">{{ currentItem.tel }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">邮箱</div>
          <div class="detail-value">{{ currentItem.email }}</div>
        </div>
        <div class="detail-item" v-if="currentItem.specialization">
          <div class="detail-label">专业方向</div>
          <div class="detail-value">{{ currentItem.specialization }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">提交时间</div>
          <div class="detail-value">{{ currentItem.create_time }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">状态</div>
          <div class="detail-value" :style="{color: getStatusColor(currentItem.status)}">
            {{ currentItem.status_text }}
          </div>
        </div>
        <div class="detail-item" v-if="currentItem.remark">
          <div class="detail-label">回复内容</div>
          <div class="detail-value">
            <el-input
              v-model="currentItem.remark"
              type="textarea"
              :rows="4"
              readonly
              class="remark-textarea"
            />
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<style scoped lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: url("@/assets/images/common_mobile/bg_user.png") no-repeat;
  background-size: cover;

  .box {
    .tabs {
      font-size: 13px;
      margin-top: 7px;
      padding: 13px 15px 9px;
      box-sizing: border-box;
      
      .tab {
        border-bottom: 2px solid transparent;
        
        &.active {
          color: $maincolor;
          border-color: $maincolor;
        }
      }
    }
    
    .list {
      padding: 12px 10px;
      box-sizing: border-box;
      
      .items {
        .item {
          padding: 12px;
          box-sizing: border-box;
          border-radius: 12px;
          box-shadow: 0 1px 6px 0 #ECEAF6;

          & + .item {
            margin-top: 12px;
          }

          .img {
            width: 80px;
            height: 50px;
            border-radius: 5px;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .info {
            .title {
              font-weight: 500;

              .name {
                font-size: 16px;
                flex: 1;
              }

              .status {
                font-size: 12px;
                margin-left: 12px;
              }
            }

            .contact-info {
              margin-top: 12px;

              .contact-item {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                font-size: 12px;
                color: #666;

                .label {
                  color: #999;
                  margin-right: 6px;
                }

                .separator {
                  margin: 0 8px;
                  color: #ddd;
                }

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }

            .tools {
              margin-top: 12px;
              font-size: 12px;
              color: #909090;
            }
          }
        }
        
        .empty-state {
          text-align: center;
          padding: 60px 0;
          
          .empty-text {
            font-size: 14px;
            color: #999;
          }
        }
      }
      
      .paginates {
        margin-top: 10px;
      }
    }
  }
}

// 详情抽屉样式
:deep(.consultation-detail-drawer) {
  .el-drawer__body {
    padding: 0;
    background: #F4F6F9;
  }

  .detail-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .detail-header {
      background: white;
      padding: 20px 15px;
      border-bottom: 1px solid #f0f0f0;

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        text-align: center;
      }
    }

    .detail-body {
      flex: 1;
      padding: 15px;

      .detail-item {
        background: white;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;

        .detail-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .detail-value {
          font-size: 16px;
          color: #333;
          word-break: break-all;
          line-height: 1.4;

          .cover-image {
            width: 120px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
            border: 1px solid #e0e0e0;
            margin-top: 8px;
            display: block;
          }

          .remark-textarea {
            margin-top: 8px;

            :deep(.el-textarea__inner) {
              background-color: #f9f9f9;
              border: 1px solid #e0e0e0;
              color: #333;
              resize: none;
              font-size: 14px;
            }
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
