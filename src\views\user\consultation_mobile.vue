<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import { backPage } from "@/utils/router.ts";
import paginateMobile from "@/components/paginateMobile.vue";
// import { myConsultations } from '@/api/common.ts'

const tabs = ref([
  {name: '全部', value: ''},
  {name: '待处理', value: 'pending'},
  {name: '已完成', value: 'completed'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref([])
const total = ref(0)

// 模拟数据，实际应该调用API
const mockData = [
  {
    id: 1,
    title: '关于论文写作的问题',
    content: '请问如何提高论文的学术水平？',
    status: 'pending',
    status_text: '待处理',
    created_at: '2024-01-15 10:30',
    expert_name: '张教授',
    reply_content: '',
    reply_time: ''
  },
  {
    id: 2,
    title: '研究方法咨询',
    content: '定量研究和定性研究的区别是什么？',
    status: 'completed',
    status_text: '已完成',
    created_at: '2024-01-14 14:20',
    expert_name: '李教授',
    reply_content: '定量研究主要通过数字和统计分析来研究现象，而定性研究则通过观察、访谈等方式深入了解现象的本质...',
    reply_time: '2024-01-14 16:45'
  }
]

const getMyConsultations = () => {
  // 模拟API调用
  // myConsultations({
  //   status: tabs.value[tabCur.value].value,
  //   page: page.value,
  //   limit: limit.value
  // }).then(res => {
  //   total.value = res.data.total
  //   list.value = res.data.list
  // })
  
  // 使用模拟数据
  const filteredData = tabs.value[tabCur.value].value 
    ? mockData.filter(item => item.status === tabs.value[tabCur.value].value)
    : mockData
  
  total.value = filteredData.length
  list.value = filteredData
}

const changePage = (e: number) => {
  page.value = e
  getMyConsultations()
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return '#F56C6C'
    case 'completed':
      return '#67C23A'
    default:
      return '#409EFF'
  }
}

watch(tabCur, (val) => {
  page.value = 1
  list.value = []
  getMyConsultations()
}, {
  deep: true
})

onMounted(() => {
  getMyConsultations()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 flex flex-column hidden">
      <div class="tabs bg-white flex align-center justify-between">
        <div class="tab" :class="{active: tabCur === index}" v-for="(item, index) in tabs" :key="index" @click="tabCur = index">
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="list flex-1 flex flex-column hidden">
        <div class="items flex-1 auto">
          <div class="item bg-white flex justify-between" v-for="(item, index) in list" :key="index">
            <div class="img">
              <img :src="'https://via.placeholder.com/60x40'" :alt="item.title">
            </div>
            <div class="info flex-1">
              <div class="title flex align-center justify-between">
                <div class="name">
                  <span>{{ item.title }}</span>
                </div>
              </div>
              <div class="price flex justify-between">
                <div class="money">
                  <span>¥{{ item.amount || '299.00' }}</span>
                </div>
                <div class="right flex align-center">
                  <div class="tools">
                    <template v-if="item.status === 'pending'">
                      <span>查看详情</span>
                    </template>
                    <template v-if="item.status === 'completed'">
                      <span>查看回复</span>
                    </template>
                  </div>
                  <div class="status" :style="{color: getStatusColor(item.status)}">
                    <span>{{ item.status_text }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="empty-state" v-if="list.length === 0">
            <div class="empty-text">暂无咨询记录</div>
          </div>
        </div>
        <div class="paginates" v-if="total > 0">
          <paginateMobile :limit="limit" :total="total" @change="changePage" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: url("@/assets/images/common_mobile/bg_user.png") no-repeat;
  background-size: cover;

  .box {
    .tabs {
      font-size: 13px;
      margin-top: 7px;
      padding: 13px 15px 9px;
      box-sizing: border-box;
      
      .tab {
        border-bottom: 2px solid transparent;
        
        &.active {
          color: $maincolor;
          border-color: $maincolor;
        }
      }
    }
    
    .list {
      padding: 12px 10px;
      box-sizing: border-box;
      
      .items {
        .item {
          padding: 12px;
          box-sizing: border-box;
          border-radius: 12px;
          box-shadow: 0 1px 6px 0 #ECEAF6;

          & + .item {
            margin-top: 12px;
          }

          .img {
            width: 100px;
            height: 63px;
            border-radius: 5px;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .info {
            .title {
              font-weight: 500;

              .name {
                font-size: 16px;
              }
            }

            .price {
              margin-top: 19px;

              .money {
                font-size: 14px;
                color: #525252;
              }

              .right {
                .tools {
                  font-size: 12px;
                  color: #909090;

                  span {
                    & + span {
                      padding-left: 16px;
                    }
                  }
                }

                .status {
                  margin-left: 16px;
                  font-size: 12px;
                }
              }
            }
          }
        }
        
        .empty-state {
          text-align: center;
          padding: 60px 0;
          
          .empty-text {
            font-size: 14px;
            color: #999;
          }
        }
      }
      
      .paginates {
        margin-top: 10px;
      }
    }
  }
}
</style>
