<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import { backPage } from "@/utils/router.ts";
import paginateMobile from "@/components/paginateMobile.vue";
import { myConsultations } from '@/api/common.ts'

// 定义咨询项类型
interface ConsultationItem {
  id: number
  name: string
  tel: string
  email: string
  content_id: number
  content_title: string
  cover_image: string
  price: number
  specialization: string
  status: string
  status_text: string
  create_time: string
  remark?: string  // 回复内容
}

const tabs = ref([
  {name: '全部', value: ''},
  {name: '待处理', value: '0'},
  {name: '已完成', value: '1'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref<ConsultationItem[]>([])
const total = ref(0)

// 详情弹窗相关
const dialogDetailVisible = ref(false)
const currentItem = ref<ConsultationItem | null>(null)

const getMyConsultations = () => {
  myConsultations({
    status: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.total
    list.value = res.data.list
  })
}

const changePage = (e: number) => {
  page.value = e
  getMyConsultations()
}

// 查看详情
const viewDetail = (item: ConsultationItem) => {
  currentItem.value = item
  dialogDetailVisible.value = true
}

const getStatusColor = (status: string) => {
  switch (status) {
    case '0':
      return '#F56C6C'  // 待处理
    case '1':
      return '#67C23A'  // 已完成
    default:
      return '#409EFF'
  }
}

watch(tabCur, () => {
  page.value = 1
  list.value = []
  getMyConsultations()
}, {
  deep: true
})

onMounted(() => {
  getMyConsultations()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 flex flex-column hidden">
      <div class="tabs bg-white flex align-center justify-between">
        <div class="tab" :class="{active: tabCur === index}" v-for="(item, index) in tabs" :key="index" @click="tabCur = index">
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="list flex-1 flex flex-column hidden">
        <div class="items flex-1 auto">
          <div class="item bg-white flex" v-for="(item, index) in list" :key="index">
            <div class="img">
              <img :src="item.cover_image" :alt="item.content_title">
            </div>
            <div class="info flex-1">
              <div class="title flex align-center justify-between">
                <div class="name">
                  <span>{{ item.content_title }}</span>
                </div>
                <div class="status" :style="{color: getStatusColor(item.status)}">
                  <span>{{ item.status_text }}</span>
                </div>
              </div>
              <div class="contact-info">
                <div class="contact-item">
                  <span class="label">联系人：</span>
                  <span>{{ item.name }}</span>
                  <span class="separator">|</span>
                  <span class="label">电话：</span>
                  <span>{{ item.tel }}</span>
                </div>
                <div class="contact-item">
                  <span class="label">时间：</span>
                  <span>{{ item.create_time }}</span>
                </div>
              </div>
              <div class="tools">
                <template v-if="item.status === '0'">
                  <span @click="viewDetail(item)">查看详情</span>
                </template>
                <template v-if="item.status === '1'">
                  <span @click="viewDetail(item)">查看回复</span>
                </template>
              </div>
            </div>
          </div>
          <div class="empty-state" v-if="list.length === 0">
            <div class="empty-text">暂无咨询记录</div>
          </div>
        </div>
        <div class="paginates" v-if="total > 0">
          <paginateMobile :limit="limit" :total="total" @change="changePage" />
        </div>
      </div>
    </div>
  </div>

  <!-- 详情抽屉 -->
  <el-drawer
    v-model="dialogDetailVisible"
    :show-close="false"
    size="100%"
    class="consultation-detail-drawer mobile-buy-pop"
    direction="btt"
  >
    <template #header="{ close }">
      <HeaderTitle @click="close"></HeaderTitle>
    </template>
    <div class="buy-info" v-if="currentItem">
      <!-- 第一块：标题和服务封面 -->
      <div class="pay-box bg-white">
        <div class="title-cover-container">
          <div class="cover-container">
            <img :src="currentItem.cover_image" :alt="currentItem.content_title" class="cover-image"
                 style="width: 120px !important; height: 160px !important; object-fit: cover !important; pointer-events: none !important; user-select: none !important; -webkit-user-select: none !important; -moz-user-select: none !important; -ms-user-select: none !important;">
          </div>
          <div class="title-container">
            <div class="title">{{ currentItem.content_title }}</div>
          </div>
        </div>
      </div>

      <!-- 第二块：联系信息 -->
      <div class="pay-box bg-white">
        <div class="title">联系信息</div>
        <el-form label-position="left" label-width="auto">
          <el-form-item label="联系人">
            <el-input :value="currentItem.name" readonly disabled />
          </el-form-item>
          <el-form-item label="电话">
            <el-input :value="currentItem.tel" readonly disabled />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input :value="currentItem.email || '未填写'" readonly disabled />
          </el-form-item>
          <el-form-item label="专业方向" v-if="currentItem.specialization">
            <el-input :value="currentItem.specialization" readonly disabled />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第三块：状态信息 -->
      <div class="pay-box bg-white">
        <div class="title">状态信息</div>
        <el-form label-position="left" label-width="auto">
          <el-form-item label="提交时间">
            <el-input :value="currentItem.create_time" readonly disabled />
          </el-form-item>
          <el-form-item label="状态">
            <el-input :value="currentItem.status_text" readonly disabled />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第四块：回复内容 -->
      <div class="pay-box bg-white" v-if="currentItem.remark">
        <div class="title">回复内容</div>
        <div class="remark-container">
          <div class="remark-content">
            {{ currentItem.remark }}
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<style scoped lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: url("@/assets/images/common_mobile/bg_user.png") no-repeat;
  background-size: cover;

  .box {
    .tabs {
      font-size: 13px;
      margin-top: 7px;
      padding: 13px 15px 9px;
      box-sizing: border-box;
      
      .tab {
        border-bottom: 2px solid transparent;
        
        &.active {
          color: $maincolor;
          border-color: $maincolor;
        }
      }
    }
    
    .list {
      padding: 12px 10px;
      box-sizing: border-box;
      
      .items {
        .item {
          padding: 12px;
          box-sizing: border-box;
          border-radius: 12px;
          box-shadow: 0 1px 6px 0 #ECEAF6;

          & + .item {
            margin-top: 12px;
          }

          .img {
            width: 80px !important;
            height: 50px !important;
            border-radius: 5px;
            margin-right: 10px;

            img {
              width: 100% !important;
              height: 100% !important;
              object-fit: cover !important;
            }
          }

          .info {
            .title {
              font-weight: 500;

              .name {
                font-size: 16px;
                flex: 1;
              }

              .status {
                font-size: 12px;
                margin-left: 12px;
              }
            }

            .contact-info {
              margin-top: 12px;

              .contact-item {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                font-size: 12px;
                color: #666;

                .label {
                  color: #999;
                  margin-right: 6px;
                }

                .separator {
                  margin: 0 8px;
                  color: #ddd;
                }

                &:last-child {
                  margin-bottom: 0;
                }
              }
            }

            .tools {
              margin-top: 12px;
              font-size: 12px;
              color: #909090;
            }
          }
        }
        
        .empty-state {
          text-align: center;
          padding: 60px 0;
          
          .empty-text {
            font-size: 14px;
            color: #999;
          }
        }
      }
      
      .paginates {
        margin-top: 10px;
      }
    }
  }
}

// 详情抽屉样式 - 照搬咨询服务样式
:deep(.consultation-detail-drawer) {
  .el-drawer__body {
    padding: 0;
    background: #F4F6F9;
  }

  // 确保咨询详情的 pay-box 有正确的分隔
  .buy-info .pay-box {
    // 继承全局样式，确保有分隔效果
    &:first-child {
      margin-top: 0; // 第一个不需要上边距
    }
  }

  // 咨询提示样式
  .consultation-tips {
    .tip-item {
      display: flex;
      align-items: center;
      padding: 8px 0;

      .icon {
        font-size: 16px;
        margin-right: 8px;
        width: 20px;
        text-align: center;
      }

      .text {
        color: #535871;
        font-size: 14px;
        line-height: 1.5;
      }

      &:not(:last-child) {
        border-bottom: 1px solid #F5F7FA;
      }
    }
  }

  // 只读表单样式
  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-input {
        .el-input__wrapper {
          background-color: #F7FAFE;
          box-shadow: unset;
          border: 1px solid #dcdfe6;

          .el-input__inner {
            color: #333;
            cursor: default;
          }
        }

        &.is-disabled .el-input__wrapper {
          background-color: #F7FAFE;

          .el-input__inner {
            color: #333;
          }
        }
      }
    }
  }

  // 标题和封面容器样式 - 使用更具体的选择器
  .buy-info .pay-box .title-cover-container {
    display: flex !important;
    align-items: flex-start !important;
    gap: 15px !important;
  }

  .buy-info .pay-box .cover-container {
    flex-shrink: 0 !important;
    width: 120px !important;

    .cover-image {
      width: 100% !important;
      height: auto !important;
      border-radius: 8px !important;
      object-fit: cover !important;
      border: 1px solid #e0e0e0 !important;
      pointer-events: none !important; /* 禁止图片交互，防止缩放 */
      user-select: none !important; /* 禁止选择 */
      -webkit-user-select: none !important; /* Safari */
      -moz-user-select: none !important; /* Firefox */
      -ms-user-select: none !important; /* IE/Edge */
    }
  }

  .buy-info .pay-box .title-container {
    flex: 1 !important;
    min-width: 0 !important; /* 允许文字换行 */
  }

  .buy-info .pay-box .title {
    font-size: 18px !important;
    font-weight: bold !important;
    color: #333 !important;
    line-height: 1.4 !important;
    word-wrap: break-word !important;
    margin: 0 !important;
  }

  // 回复内容样式
  .remark-container {
    .remark-content {
      background-color: #F7FAFE;
      padding: 12px;
      border-radius: 8px;
      line-height: 1.5;
      min-height: 60px;
      font-size: 14px;
      color: #333;
    }

    .remark-empty {
      color: #999;
      font-style: italic;
      font-size: 14px;
      text-align: center;
      padding: 20px 0;
    }
  }
}
</style>
