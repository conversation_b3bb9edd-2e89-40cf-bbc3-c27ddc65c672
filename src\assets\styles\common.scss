@use "utils.scss" as *;
.container{
  width: px2rem(1200);
  margin:0 auto;
}
.text-white{color: $whitecolor}
.bg-white{background-color: $whitecolor;}

.text-center{text-align: center}

.flex{display: flex;}
.flex-column{flex-direction: column}
.flex-1{flex:1;}
.justify-between{justify-content: space-between;}
.justify-center{justify-content: center;}
.justify-around{justify-content: space-around;}
.justify-end{justify-content: flex-end;}
.justify-start{justify-content: start;}
.align-center{align-items: center;}
.align-end{align-items: flex-end;}

.position-relative{position: relative;}
.position-absolute{position: absolute;}
.position-fixed{position: fixed;}

.cursor-pointer{cursor: pointer;}

.mt-1{margin-top: px2rem(10)}
.mt-2{margin-top: px2rem(20)}
.mt-3{margin-top: px2rem(30)}

.ml-1{margin-left: px2rem(10)}
.ml-2{margin-left: px2rem(20)}
.ml-3{margin-left: px2rem(30)}

.mr-1{margin-right: px2rem(10)}

.my-1{margin-top: px2rem(10);margin-bottom: px2rem(10);}

.px-1{padding-left: px2rem(10);padding-right: px2rem(10);}

.pt-2{padding-top: px2rem(20);}

.hidden{overflow: hidden;}
.auto{overflow: auto;}

.row1,.row2,.row3,.row4,.row5{display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical}
.row1{-webkit-line-clamp:1}
.row2{-webkit-line-clamp:2}
.row3{-webkit-line-clamp:3}
.row4{-webkit-line-clamp:4}
.row5{-webkit-line-clamp:5}
.el-dialog{
  .el-dialog__header{
    padding: unset;
  }
  .el-dialog__body{
    height: 100%;
    padding: px2rem(20);
    box-sizing: border-box;
  }
}
.el-link{
  font-weight: normal;
  .el-link__inner{
    &:hover{
      color: #3B78C8;
    }
  }
}

// 隐藏滚动条
::-webkit-scrollbar {
  display: none;
}

// 兼容 Firefox
* {
  scrollbar-width: none;
}

// 兼容 IE
* {
  -ms-overflow-style: none;
}