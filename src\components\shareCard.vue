<script setup lang="ts">
import { ref } from 'vue'
import QRCode from "qrcode";
import {Star, StarFilled} from "@element-plus/icons-vue";
import {shareToQQ, shareToWeibo} from '@/utils/utils.ts'
const { title } = defineProps({
  title: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  }
});
const collect = ref(false)
const dialogVisible = ref(false);
const qrCode = ref('')
const generateQRCode = async (): Promise<string> => {
  return await QRCode.toDataURL(location.href);
};
const shareToWechat = async () => {
  qrCode.value = await generateQRCode()
  dialogVisible.value = true
}
</script>

<template>
  <div class="tools flex justify-between align-center">
    <slot />
    <div class="share flex align-center">
      <div class="name">
        <span>分享到</span>
      </div>
      <div class="icon flex align-center cursor-pointer" @click="shareToWechat()">
        <img src="@/assets/images/common/icon_wechat.png" alt="">
        <span>微信</span>
      </div>
      <div class="icon flex align-center cursor-pointer" @click="shareToWeibo(title, image)">
        <img src="@/assets/images/common/icon_sina.png" alt="">
        <span>微博</span>
      </div>
      <div class="icon flex align-center cursor-pointer" @click="shareToQQ(title, image)">
        <img src="@/assets/images/common/icon_tencent.png" alt="">
        <span>QQ</span>
      </div>
    </div>
  </div>
  <el-dialog
      v-model="dialogVisible"
      width="300"
      title="请使用微信扫码分享给好友"
      class="sharePop"
  >
    <div class="image flex align-center justify-center">
      <el-image :src="qrCode" fit="scale-down"></el-image>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.tools{
  margin-top: px2rem(20);

  .share{
    color: #93999A;
    font-size: px2rem(16);
    .name{

    }
    .icon{
      margin-left: px2rem(20);
      img{
        width: px2rem(22);
        height: px2rem(22);
        margin-right: px2rem(6);
      }
    }
  }
}
</style>