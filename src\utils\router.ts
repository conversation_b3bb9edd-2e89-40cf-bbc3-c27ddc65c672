import router from '../router'
import type { RouteLocationRaw } from 'vue-router'
import { ElMessage } from 'element-plus'

interface SafePushOptions {
    silent?: boolean // 是否静默（不报错）
    onSuccess?: () => void
    onError?: (err: any) => void
}

export function safePush(to: RouteLocationRaw, options: SafePushOptions = {}) {
    const { silent = false, onSuccess, onError } = options

    let exists = false

    if (typeof to === 'object' && to !== null) {
        if ('name' in to && typeof to.name === 'string') {
            exists = router.hasRoute(to.name)
        } else if ('path' in to && typeof to.path === 'string') {
            exists = router.getRoutes().some(route => route.path === to.path)
        }
    } else {
        exists = router.getRoutes().some(route => route.path === to)
    }

    if (!exists) {
        if (!silent) {
            ElMessage.error('路由不存在')
            console.warn('[safePush] 路由不存在:', to)
        }
        return
    }

    router.push(to)
        .then(() => {
            onSuccess?.()
        })
        .catch(err => {
            if (!silent) {
                ElMessage.error('路由跳转失败:' + err)
                console.error('[safePush] 路由跳转失败:', err)
            }
            onError?.(err)
        })
}

/**
 * 路由返回上一页
 */
export function backPage(){
    router.back()
}