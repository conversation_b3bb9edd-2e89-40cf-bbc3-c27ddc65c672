<script setup lang="ts">
import {safePush} from "@/utils/router.ts";

const props = defineProps({
  title: {
    type: String,
    required: true,
    default: '',
  },
  icon: {
    type: String,
    required: false,
    default: '',
  },
  moreUrl: {
    type: String,
    required: false,
    default: '',
  }
})
</script>

<template>
  <div class="abouts">
    <div class="name-bar flex align-center">
      <img :src="props.icon" alt="">
      <span>{{props.title}}</span>
    </div>
    <slot />
    <div class="more cursor-pointer flex align-center justify-center" @click="safePush(props.moreUrl)" v-if="props.moreUrl">
      <span>加载更多</span>
      <img src="@/assets/images/common/icon_more.png" alt="">
    </div>
  </div>
</template>

<style scoped lang="scss">
.abouts{
  .name-bar{
    color: #191B1F;
    font-size: px2rem(20);
    font-weight: bold;
    margin-bottom: px2rem(16);
    img{
      width: px2rem(24);
      height: px2rem(24);
      margin-right: px2rem(10);
    }
  }
  .more{
    color: #5C6671;
    font-size: px2rem(16);
    margin: px2rem(40) auto;
    img{
      width: px2rem(20);
      height: px2rem(20);
      margin-left: px2rem(2);
    }
  }
}
</style>