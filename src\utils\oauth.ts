import { useUserInfoStore } from '@/store/userInfo'

/**
 * 检测是否在微信浏览器中
 */
export const isWeixinBrowser = (): boolean => {
  if (typeof window === 'undefined') return false
  const userAgent = navigator.userAgent.toLowerCase()
  return /micromessenger/.test(userAgent)
}

/**
 * 检测是否为移动端
 */
export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false
  const userAgent = navigator.userAgent.toLowerCase()
  return /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
}

/**
 * 第三方登录入口
 * @param name 登录平台名称 (qq, wechat_scan, wechat_mp)
 * @param type 登录类型 (login, bind)
 */
export const onLogin = (name: string, type: 'login' | 'bind' = 'login') => {
  const userInfoStore = useUserInfoStore()
  const baseUrl = import.meta.env.VITE_API_URL
  const token = userInfoStore.userInfo.token || ''
  
  // 构建授权URL
  const url = `${baseUrl}/api/OAuthLogin/index?server=1&name=${name}&type=${type}&token=${token}`

  if (type === 'bind' && !isMobile()) {
    // 账号绑定模式，在新窗口中打开
    const popup = window.open(
      url, 
      '_blank', 
      'height=600,width=760,top=20,left=20,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no'
    )

    // 监听绑定完成消息
    const messageHandler = (event: MessageEvent) => {
      if (event.data === 'bind-done') {
        window.removeEventListener('message', messageHandler)
        popup?.close()
        window.location.reload()
      }
    }
    window.addEventListener('message', messageHandler)
  } else {
    // 登录模式，直接跳转
    window.location.href = url
  }
}

/**
 * 获取OAuth登录二维码
 * @param name 登录平台名称
 * @param callback 获取二维码后的回调函数
 */
export const getOauthQrcode = (name: string, callback?: (qrcode: string) => void) => {
  const userInfoStore = useUserInfoStore()
  const baseUrl = import.meta.env.VITE_API_URL
  const token = userInfoStore.userInfo.token || ''
  
  // 对于微信扫码登录，可以通过特定接口获取二维码
  if (name === 'wechat_scan') {
    const qrcodeUrl = `${baseUrl}/api/OAuthLogin/getWechatQrcode?token=${token}`
    if (callback) {
      callback(qrcodeUrl)
    }
    return qrcodeUrl
  }
  
  return null
}

/**
 * 检查OAuth登录状态
 * @param callback 状态检查回调
 */
export const checkOauthLoginStatus = (callback?: (success: boolean, data?: any) => void) => {
  // 可以实现轮询检查登录状态的逻辑
  // 这里先预留接口
  if (callback) {
    callback(false)
  }
}

/**
 * 处理OAuth登录回调
 * @param code 授权码
 * @param state 状态参数
 * @param autoRedirect 是否自动重定向（默认false）
 */
export const handleOauthCallback = async (code: string, state: string, autoRedirect: boolean = false) => {
  try {
    const { loginAgent } = await import('@/api/oauth')
    const response: any = await loginAgent(code, state)
    
    if (response && response.code === 1) {
      // 登录成功，更新用户信息
      const userInfoStore = useUserInfoStore()
      userInfoStore.setUserInfo(response.data.userInfo)
      userInfoStore.setIsLogin(true)
      
      // 如果是绑定模式，发送消息通知父窗口
      if (response.data.type === 'bind') {
        if (window.opener) {
          window.opener.postMessage('bind-done', '*')
        }
        window.close()
      } else if (autoRedirect) {
        // 只有在明确要求自动重定向时才重定向
        const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/'
        window.location.href = redirectUrl
      }
    }
    
    return response
  } catch (error) {
    console.error('OAuth callback error:', error)
    throw error
  }
} 