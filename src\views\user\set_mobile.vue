<script setup lang="ts">
import { ref, onMounted } from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import { useUserInfoStore } from '@/store/userInfo.ts'
import type {FormInstance} from "element-plus";
import {backPage} from "@/utils/router.ts";
const userInfoStore = useUserInfoStore();
const formRef = ref()
const phoneRef = ref()
const form = ref({
  phone: '',
  wechat: '',
})
const formPhone = ref({
  phone: '',
  code: ''
})
const showPop = ref(false)
const rules = ref({
  phone: [
    {  required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ],
  code: [
    {  required: true, message: '请输入验证码', trigger: 'blur' },
  ]
})
const openPop = () => {
  showPop.value = true
}
const getCode = () => {

}
const confirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
    }
  })
}
onMounted(() => {
  form.value.phone = JSON.parse(JSON.stringify(userInfoStore.userInfo))?.mobile
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 auto">
      <div class="card bg-white">
        <div class="form">
          <el-form ref="formRef" :model="form" label-width="100px" label-position="left">
            <el-form-item label="手机号" prop="phone">
              <el-input type="number" maxlength="11" v-model="form.phone" disabled placeholder="请输入手机号">
                <template #append>
                  <el-button @click="openPop">修改</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="微信号" prop="wechat">
              <el-input type="number" v-model="form.wechat" placeholder="请输入微信号" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
  <el-drawer v-model="showPop" :show-close="false" size="100%" class="service-filters-popup">
    <template #header="{ close }">
      <HeaderTitle @click="close" />
    </template>
    <div class="popup">
      <el-form ref="phoneRef" :model="formPhone" :rules="rules">
        <el-form-item label="" prop="phone">
          <el-input maxlength="11" v-model="formPhone.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="" prop="code">
          <el-input maxlength="6" v-model="formPhone.code" placeholder="请输入验证码">
            <template #append>
              <el-button :disabled="formPhone.phone != '' || formPhone.code != ''" @click="getCode">获取验证码</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <div class="confirm flex-1">
            <el-button type="primary" @click="confirm(phoneRef)">修改</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>

<style scoped lang="scss">
.page {
  background: #F6F7FA;
  height: 100vh;

  .box {
    padding: 15px;
    box-sizing: border-box;
    .card {
      padding: 30px 20px 20px;
      box-sizing: border-box;
      border-radius: 12px;
      box-shadow: 0 1px 6px 0 #ECEAF6;
      .form{
        :deep(.el-form){
          .el-form-item{
            margin-bottom: 20px;
            .el-form-item__label{
              font-size: 14px;
              width: 70px !important;
            }
            .el-form-item__content{
              .el-input{
                .el-input__wrapper{
                  background-color: #F7FAFE;
                  box-shadow: unset;
                  .el-input__inner{
                    font-size: 14px;
                  }
                }
                .el-input-group__append{
                  box-shadow: unset;
                  background-color: #F7FAFE;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
}
.popup{
  padding: 30px 40px;
  box-sizing: border-box;
  :deep(.el-form){
    .el-form-item{
      margin-bottom: 26px;
      box-shadow: 0 1px 6px 0 #ECEAF6;
      border-radius: 8px;
      &:last-child{
        margin-bottom: 0;
      }
      .el-form-item__content{
        .el-input{
          .el-input__wrapper{
            box-shadow: unset;
            .el-input__inner{
              height: 48px;
              font-size: 14px;
            }
          }
          .el-input-group__append{
            background-color: #fff;
            color: $maincolor;
            box-shadow: unset;
            .el-button{
              font-weight: unset;
            }
          }
        }
        .confirm{
          .el-button{
            width: 100%;
            height: 48px;
            border-radius: 8px;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>