<script setup lang="ts">
import { ref, onMounted } from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import {backPage} from "@/utils/router.ts";
import { useUserInfoStore } from '@/store/userInfo.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
import {ElMessage, type FormInstance, type UploadProps} from "element-plus";
import { profile } from '@/api/common.ts'
const userInfoStore = useUserInfoStore();
const formRef = ref()
const uploadUrl = ref(`${import.meta.env.VITE_API_URL}/api/ajax/upload`)
const form = ref({
  username: '',
  avatar: '',
  nickname: '',
  gender: '0',
  birthday: '',
  email: '',
  major: '',
  school: '',
  company: '',
})
const rules = ref({

})
const confirm = async(formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      profile(form.value).then(res => {
        ElMessage.error(res.msg)
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
    response,
    uploadFile
) => {
  console.log(response)
  if (response.code === 1) {
    form.value.avatar = response.data.file.url
  }
}
onMounted( () => {
  form.value = JSON.parse(JSON.stringify(userInfoStore.userInfo))
  console.log(form.value)
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 auto">
      <div class="card bg-white">
        <div class="form">
          <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="left">
            <el-form-item label="头像" prop="reason">
              <el-upload :action="uploadUrl" :show-file-list="false" :headers="{'ba-user-token': userInfoStore.userInfo.token, server: true}" :on-success="handleAvatarSuccess">
                <template v-if="form.avatar">
                  <el-avatar :size="44" :src="isHttpOrHttps(form.avatar)" />
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="form.nickname" placeholder="请输入昵称" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio :value="0">男</el-radio>
                <el-radio :value="1">女</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="生日" prop="birthday">
              <el-date-picker
                  v-model="form.birthday"
                  type="date"
                  placeholder="请选择生日"
                  :editable="false"
              />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input type="number" v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
            <el-form-item label="专业" prop="major">
              <el-input type="number" v-model="form.major" placeholder="请输入专业" />
            </el-form-item>
            <el-form-item label="毕业院校" prop="school">
              <el-input type="number" v-model="form.school" placeholder="请输入毕业院校" />
            </el-form-item>
            <el-form-item label="工作单位" prop="company">
              <el-input type="number" v-model="form.company" placeholder="请输入工作单位" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="confirm flex-1 flex justify-center">
        <el-button type="primary" @click="confirm(formRef)">保存</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page{
  background: #F6F7FA;
  height:  100vh;
  .box{
    padding: 15px;
    box-sizing: border-box;
    .card{
      padding: 30px 20px 20px;
      box-sizing: border-box;
      border-radius: 12px;
      box-shadow: 0 1px 6px 0 #ECEAF6;
      .form{
        :deep(.el-form){
          .el-form-item{
            margin-bottom: 20px;
            .el-form-item__label{
              font-size: 14px;
              width: 70px !important;
            }
            .el-form-item__content{
              .el-input{
                .el-input__wrapper{
                  background-color: #F7FAFE;
                  box-shadow: unset;
                  .el-input__inner{
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }

      }
    }
    .confirm{
      margin-top: 30px;
      .el-button{
        width: 120px;
        height: unset;
        line-height: 1;
        padding: 11px 0;
        box-sizing: border-box;
        border-radius: 8px;
      }
    }
  }
}
</style>