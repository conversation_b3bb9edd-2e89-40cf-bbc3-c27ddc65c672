<script setup lang="ts">
import {ref, computed, onMounted} from 'vue'
import type { FormInstance } from 'element-plus'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import { cancelOrder, orderDetail, submitRefund } from '@/api/common'
import { ElMessage } from 'element-plus'
import {backPage} from "@/utils/router.ts";
import { useRoute } from 'vue-router'
import {isHttpOrHttps} from "@/utils/utils.ts";
const route = useRoute();
const formRef = ref()
const form = ref({
  reason: '',
  money: ''
})
const orders = ref({})
const getStatusColor = (status: string) => {
  let color = '';
  switch (status) {
    case 'pending':
    case '待付款':
      color = '#26B84C';
      break;
    case 'paid':
    case 'in_progress':
    case '待完成':
      color = '#F1A92E';
      break;
    case 'completed':
    case '已完成':
      color = '#1F1F1F';
      break;
    case 'cancelled':
    case 'refunded':
    case '售后中':
      color = '#E5331B';
      break;
    default:
      color = '#1F1F1F';
      break;
  }
  return color;
}
const rules = ref({
  reason: [
    { required: true, message: '请输入退款原因', trigger: 'blur' },
  ],
  money: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { validator: (rule: any, value: any, callback: any) => {
        console.log(rule)
        if (value <= 0 || value > orders.value.total_amount) {
          callback(new Error('退款金额不正确'))
        }
        callback()
      }, trigger:  'blur'
    }
  ]
})
const isDisabled = computed(() => {
  if (!form.value.reason || !form.value.money) {
    return true
  }
  return false
})
const confirm = async(formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      submitRefund({
        order_id: orders.value.id,
        refund_type: 'refund_only',
        refund_amount: form.value.money,
        reason: form.value.reason
      }).then(res=>{
        ElMessage.success(res.msg)
        backPage()
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const getDetails = () => {
  orderDetail({
    order_id: route.query.id,
  }).then(res => {
    orders.value = res.data
  })
}
onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="boxs">
      <div class="container bg-white">
        <div class="title text-center">
          <span>售后</span>
        </div>
        <div class="box flex justify-between">
          <div class="img hidden">
            <img :src="isHttpOrHttps(orders.cover_image)" alt="">
          </div>
          <div class="info flex-1 position-relative">
            <div class="title flex align-center justify-between">
              <div class="name">
                <span>{{ orders.service_title }}</span>
              </div>
              <div class="status" :style="{color: getStatusColor(orders.status)}">
                <span>{{ orders.status_text }}</span>
              </div>
            </div>
            <div class="money">
              <span>¥{{ orders.total_amount }}</span>
            </div>
          </div>
        </div>
        <div class="form">
          <el-form ref="formRef" :model="form" :rules="rules" :label-width="180">
            <el-form-item label="退款原因" prop="reason">
              <el-input type="textarea" :rows="4" v-model="form.reason" placeholder="请输入退款原因" />
            </el-form-item>
            <el-form-item label="退款金额" prop="money">
              <el-input type="number" v-model="form.money" :min="0" placeholder="请输入退款金额" />
            </el-form-item>
            <el-form-item label="">
              <div class="flex-1 flex justify-center">
                <el-button :disabled="isDisabled" type="primary" @click="confirm(formRef)">提交</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .boxs{
    margin: px2rem(20) 0 px2rem(100);
    .container{
      padding: px2rem(40) px2rem(170) px2rem(50);
      box-sizing: border-box;
      border-radius: px2rem(16);
      .title{
        color: #1F1F1F;
        font-size: px2rem(24);
        font-weight: 500;
      }
      .box{
        margin: px2rem(50) 0 px2rem(40);
        background: #F4F6F9;
        border-radius: px2rem(8);
        padding: px2rem(20);
        box-sizing: border-box;
        .img{
          width: px2rem(158);
          height: px2rem(100);
          border-radius: px2rem(10);
          margin-right: px2rem(20);
          img{
            width: 100%;
          }
        }
        .info{
          .title{
            font-weight: 500;
            .name{
              font-size: px2rem(22);
            }
            .status{
              font-size: px2rem(18);
            }
          }
          .money{
            font-size: px2rem(20);
            color: #525252;
            margin-top: px2rem(12);
          }
        }
      }
      .form{
        .el-button{
          width: px2rem(200);
          height: unset;
          line-height: 1;
          padding: px2rem(15) px2rem(80);
          box-sizing: border-box;
          border-radius: px2rem(8);
          margin-top: px2rem(60);
        }
      }
    }
  }
}
</style>