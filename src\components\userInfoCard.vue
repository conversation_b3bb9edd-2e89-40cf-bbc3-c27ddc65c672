<script setup lang="ts">

import { useUserInfoStore } from '@/store/userInfo.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
const userInfoStore = useUserInfoStore()
</script>

<template>
  <div class="info flex align-center bg-white">
    <div class="avatar hidden">
      <img :src="isHttpOrHttps(userInfoStore.userInfo.avatar)" alt="">
    </div>
    <div class="nickname">
      <span>{{userInfoStore.userInfo.nickname}}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.info{
  padding: px2rem(20);
  box-sizing: border-box;
  .avatar{
    width: px2rem(80);
    height: px2rem(80);
    border-radius: 50%;
    img{
      width: 100%;
    }
  }
  .nickname{
    margin-left: px2rem(22);
    font-size: px2rem(28);
    color: #11243E;
    font-weight: 500;
  }
}
</style>