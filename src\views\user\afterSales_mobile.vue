<script setup lang="ts">
import {ref, onMounted, computed} from 'vue'
import type {FormInstance} from 'element-plus'
import HeaderTitle from "@/components/headerTitle.vue";
import {backPage} from "@/utils/router.ts";
import {cancelOrder, orderDetail, submitRefund} from '@/api/common'
import {ElMessage} from 'element-plus'
import type {OrderItem} from "@/types/user.ts";
import { useRoute } from 'vue-router'
import {isHttpOrHttps} from "@/utils/utils.ts";
const route = useRoute();
const formRef = ref()
const item = ref(<OrderItem>{})
const orders = ref({})
const form = ref({
  reason: '',
  money: ''
})
const getStatusColor = (status: string) => {
  let color = '';
  switch (status) {
    case 'pending':
    case '待付款':
      color = '#26B84C';
      break;
    case 'paid':
    case 'in_progress':
    case '待完成':
      color = '#F1A92E';
      break;
    case 'completed':
    case '已完成':
      color = '#1F1F1F';
      break;
    case 'cancelled':
    case 'refunded':
    case '售后中':
      color = '#E5331B';
      break;
    default:
      color = '#1F1F1F';
      break;
  }
  return color;
}
const rules = ref({
  reason: [
    {required: true, message: '请输入退款原因', trigger: 'blur'},
  ],
  money: [
    {required: true, message: '请输入退款金额', trigger: 'blur'},
    {
      validator: (rule: any, value: any, callback: any) => {
        console.log(rule)
        if (value <= 0 || value > orders.value.total_amount) {
          callback(new Error('退款金额不正确'))
        }
        callback()
      }, trigger: 'blur'
    }
  ]
})
const isDisabled = computed(() => {
  if (!form.value.reason || !form.value.money) {
    return true
  }
  return false
})
const confirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      submitRefund({
        order_id: orders.value.id,
        refund_type: 'refund_only',
        refund_amount: form.value.money,
        reason: form.value.reason
      }).then(res=>{
        ElMessage.success(res.msg)
        backPage()
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const getDetails = () => {
  orderDetail({
    order_id: route.query.id,
  }).then(res => {
    orders.value = res.data
  })
}
onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 auto">
      <div class="card bg-white flex align-center justify-between">
        <div class="img">
          <img :src="isHttpOrHttps(orders.cover_image)" alt="">
        </div>
        <div class="info flex-1">
          <div class="title flex align-center justify-between">
            <div class="name">
              <span>{{ orders.service_title }}</span>
            </div>
          </div>
          <div class="price flex justify-between">
            <div class="money">
              <span>¥{{ orders.total_amount }}</span>
            </div>
            <div class="right flex align-center">
              <div class="status" :style="{color: getStatusColor(orders.status)}">
                <span>{{ orders.status_text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" :label-width="180">
        <div class="form">
          <div class="card bg-white">
            <el-form-item label="退款原因" prop="reason">
              <el-input type="textarea" :rows="4" v-model="form.reason" placeholder="请填写补充退款原因，有助于您更快解决问题"/>
            </el-form-item>
          </div>
          <div class="card bg-white">
            <el-form-item label="退款金额" prop="money">
              <el-input type="number" v-model="form.money" :min="0" placeholder="请输入退款金额"/>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div class="confirm flex justify-center">
        <el-button :disabled="isDisabled" type="primary" @click="confirm(formRef)">提交</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #F6F7FA;
  height: 100vh;

  .box {
    padding: 15px;
    box-sizing: border-box;

    .card {
      padding: 12px;
      box-sizing: border-box;
      border-radius: 12px;
      box-shadow: 0 1px 6px 0 #ECEAF6;

      & + .card {
        margin-top: 12px;
      }

      .img {
        width: 100px;
        height: 63px;
        border-radius: 5px;
        margin-right: 10px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .info {
        .title {
          font-weight: 500;

          .name {
            font-size: 16px;
          }

        }

        .price {
          margin-top: 19px;

          .money {
            font-size: 14px;
            color: #525252;
          }

          .right {
            .tools {
              font-size: 12px;
              color: #909090;
            }

            .status {
              margin-left: 16px;
              font-size: 12px;
            }
          }
        }


      }

    }

    :deep(.el-form) {
      .form {
        margin-top: 12px;
      }

      .el-form-item {
        .el-form-item__content {
          .el-textarea{
            .el-textarea__inner{
              box-shadow: none;
              background-color: #F5F7FA;
            }
          }

          .el-input{
            .el-input__wrapper{
              box-shadow: none;
              background-color: #F5F7FA;
            }
          }
        }
      }
    }

    .confirm {
      .el-button {
        margin: 0 15px;
        width: 100%;
        height: unset;
        line-height: 1;
        padding: 12px 0;
        box-sizing: border-box;
        border-radius: 22px;
        margin-top: 40px;
        font-size: 14px;
      }
    }
  }
}
</style>