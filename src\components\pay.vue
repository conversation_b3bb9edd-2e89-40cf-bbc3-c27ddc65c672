<script setup lang="ts">
import { ref } from 'vue'
import {getAssetsFile} from "@/utils/utils.ts";
import VueQrcode from "@chenfengyuan/vue-qrcode";
const emit = defineEmits(['close'])
defineProps({
  orderInfo: {
    type: Object,
    default: () => {}
  }
})
const dialogVisible = ref(false)
const open = () => {
  dialogVisible.value = true
}
const close = () => {
  dialogVisible.value = false
}
const handleClose = (done: () => void) => {
  emit('close')
  done()
}
defineExpose({
  open,
  close
})
</script>

<template>
  <el-dialog
      v-model="dialogVisible"
      width="400"
      title="在线快速支付"
      class="buyPop"
      :show-close="false"
      :before-close="handleClose"
  >
    <div class="buy-info text-center">
      <div class="price">
        <span>¥</span>
        <span>{{orderInfo.total_amount}}</span>
      </div>
      <div class="unit">
        <span>订单金额</span>
      </div>
      <div class="code">
        <vue-qrcode :value="orderInfo.qr_code" :options="{ width: 180, margin: 0, color: {
          dark: '#3B78C8'
        } }" />
      </div>
    </div>
  </el-dialog>

</template>

<style scoped lang="scss">

.buy-info{
  .price{
    color: #E64F26;
    font-size: px2rem(36);
    line-height: px2rem(50);
    font-weight: 600;
  }
  .unit{
    color: #93999A;
    font-size: px2rem(16);
  }
  .code{
    width: px2rem(180);
    height: px2rem(180);
    margin: px2rem(40) auto 0;
  }
}
</style>