import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persistedstate'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import '@/assets/styles/font.scss'
import "@/assets/styles/common.scss"
import './permission'
import { loginService } from './services/login'
import { loginServiceMobile } from './services/loginMobile'
import '@/utils/login'  // 导入登录工具，注册全局方法
import '@/utils/loginMobile'
// import VConsole from 'vconsole';
// new VConsole();
const pinia = createPinia()
pinia.use(piniaPersist)
const app = createApp(App)

// 注册全局服务
app.provide('loginService', loginService)
app.provide('loginServiceMobile', loginServiceMobile)

app.use(router)
app.use(pinia)
app.use(ElementPlus, {
    locale: zhCn,
})
app.mount('#app')