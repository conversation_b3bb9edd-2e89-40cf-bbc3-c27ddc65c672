import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

// https://vite.dev/config/
export default defineConfig({
  base: './',
  plugins: [
    vue()
  ],
  server: {
    host: '0.0.0.0',
    port: 5173,
    open: true,
    allowedHosts: ['yzys.dlbsdhy.com']
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use '@/assets/styles/utils.scss' as *;
          @use '@/assets/styles/variable.scss' as *;
        `
      },
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
