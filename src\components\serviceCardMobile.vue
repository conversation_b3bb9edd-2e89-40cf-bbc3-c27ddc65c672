<script setup lang="ts">
import {isHttpOrHttps} from "@/utils/utils.ts";

const { info } = defineProps({
  info: {
    type: Object,
    required: true,
  }
});
import {safePush} from "@/utils/router.ts";
</script>

<template>
  <div class="item bg-white hidden cursor-pointer" @click="safePush({path: '/service/details', query: {id: info.id}})">
    <div class="thumb">
      <el-image :src="isHttpOrHttps(info.cover_image)" fit="cover"></el-image>
    </div>
    <div class="info bg-white">
      <div class="title">
        <span v-html="info.title"></span>
      </div>
      <div class="desc flex justify-between row1">
        <span>{{info.subtitle}}</span>
      </div>
      <div class="prices flex align-center justify-between">
        <template v-if="info.price > 0">
          <div class="price">
            <span>¥ {{info.price}}</span>
          </div>
        </template>
        <template v-else>
          <div class="ask">
            <el-button link type="primary">咨询</el-button>
          </div>
        </template>
        <div class="users">
          <span>{{info.purchase_count}}人购买</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item {
  box-shadow: 0 1px 12px 0 rgba(0, 66, 190, 0.14);
  border-radius: 12px;
  width: 100%;
  height: 100%;

  .thumb {
    height: 106px;
    .el-image{
      width: 100%;
      height: 100%;
    }
  }

  .info {
    padding: 10px;
    box-sizing: border-box;

    .title {
      font-size: 14px;
      font-weight: 500;
    }

    .desc {
      margin-top: 4px;
      color: #535871;
      font-size: 12px;
    }

    .prices {
      margin-top: 10px;

      .ask{
        .el-button{
          font-size: 13px;
        }
      }

      .price {
        color: #E64F26;
        font-size: 16px;
        font-weight: 500;
      }

      .users {
        color: #696A77;
        font-size: 12px;
      }
    }
  }
}
</style>