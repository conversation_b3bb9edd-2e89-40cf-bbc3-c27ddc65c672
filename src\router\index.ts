import { createRouter, createWebHashHistory } from 'vue-router'
import { dynamicViewImport } from '@/utils/dynamicImport'

export const routes = [
  {
    path: '/:pathMatch(.*)*',
    name: '404',
    redirect: '/',
  },
  {
    path: '/',
    redirect: '/home',
  },
  {
    name: 'Home',
    path: '/home',
    component: () => dynamicViewImport('home/index'),
    meta: {
      title: '首页'
    },
  },
  {
    name: 'Service',
    path: '/service',
    redirect: '/service/index',
    children: [
      {
        name: 'ServiceIndex',
        path: 'index',
        component: () => dynamicViewImport('service/index'),
        meta: {
          title: '学术科研服务'
        },
      },
      {
        name: 'ServiceDetails',
        path: 'details',
        component: () => dynamicViewImport('service/details'),
        meta: {
          title: '学术科研服务'
        }
      }
    ]
  },
  {
    name: 'Expert',
    path: '/expert',
    redirect: '/expert/index',
    children: [
      {
        name: 'ExpertIndex',
        path: 'index',
        component: () => dynamicViewImport('expert/index'),
        meta: {
          title: '专家智库'
        },
      },
      {
        name: 'ExpertDetails',
        path: 'details',
        component: () => dynamicViewImport('expert/details'),
        meta: {
          title: '专家智库'
        }
      }
    ]
  },
  {
    name: 'Journal',
    path: '/journal',
    redirect: '/journal/index',
    children: [
      {
        name: 'JournalIndex',
        path: 'index',
        component: () => dynamicViewImport('journal/index'),
        meta: {
          title: '学术期刊'
        },
      },
      {
        name: 'JournalDetails',
        path: 'details',
        component: () => dynamicViewImport('journal/details'),
        meta: {
          title: '学术期刊'
        }
      }
    ]
  },
  {
    name: 'Stories',
    path: '/stories',
    redirect: '/stories/index',
    children: [
      {
        name: 'StoriesIndex',
        path: 'index',
        component: () => dynamicViewImport('stories/index'),
        meta: {
          title: '学术头条'
        },
      },
      {
        name: 'StoriesDetails',
        path: 'details',
        component: () => dynamicViewImport('stories/details'),
        meta: {
          title: '学术头条'
        }
      }
    ]
  },
  {
    name: 'User',
    path: '/user',
    redirect: '/user/index',
    children: [
      {
        name: 'UserIndex',
        path: 'index',
        component: () => dynamicViewImport('user/index'),
        meta: {
          title: '个人中心'
        }
      },
      {
        name: 'UserCollect',
        path: 'collect',
        component: () => dynamicViewImport('user/collect'),
        meta: {
          title: '我的收藏'
        }
      },
      {
        name: 'UserAccount',
        path: 'account',
        component: () => dynamicViewImport('user/account'),
        meta: {
          title: '我的账号'
        }
      },
      {
        name: 'UserSet',
        path: 'set',
        component: () => dynamicViewImport('user/set'),
        meta: {
          title: '账号设置'
        }
      },
      {
        name: 'UserAfterSales',
        path: 'afterSales',
        component: () => dynamicViewImport('user/afterSales'),
        meta: {
          title: '售后'
        }
      }
    ]
  },
  {
    name: 'Search',
    path: '/search',
    component: () => dynamicViewImport('search/index'),
    meta: {
      title: '搜索'
    }
  },
  {
    name: 'Cate',
    path: '/cate',
    component: () => dynamicViewImport('/cate/index'),
    meta: {
      title: ''
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
