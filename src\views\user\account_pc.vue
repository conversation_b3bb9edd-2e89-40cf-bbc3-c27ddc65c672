<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import userInfoCard from "@/components/userInfoCard.vue";
import userSliderCard from "@/components/userSliderCard.vue";
import {ElMessage, type FormInstance, type UploadProps} from "element-plus";
import { useUserInfoStore } from '@/store/userInfo.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
import { profile } from '@/api/common.ts'
const userInfoStore = useUserInfoStore();
const formRef = ref()
const uploadUrl = ref(`${import.meta.env.VITE_API_URL}/api/ajax/upload`)
const form = ref({
  username: '',
  avatar: '',
  nickname: '',
  gender: '0',
  birthday: '',
  email: '',
  major: '',
  school: '',
  company: '',
})
const rules = ref({

})
const confirm = async(formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
      profile(form.value).then(res => {
        ElMessage.error(res.msg)
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}
const handleAvatarSuccess: UploadProps['onSuccess'] = (
    response,
    uploadFile
) => {
  console.log(response)
  if (response.code === 1) {
    form.value.avatar = response.data.file.url
  }
}
onMounted( () => {
  form.value = JSON.parse(JSON.stringify(userInfoStore.userInfo))
  console.log(form.value)
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="userinfo">
      <div class="container">
        <userInfoCard />
      </div>
    </div>
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="sliders">
          <userSliderCard />
        </div>
        <div class="box bg-white flex-1">
          <div class="title">
            <span>我的资料</span>
          </div>
          <div class="form">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="left">
              <el-form-item label="头像" prop="reason">
                <el-upload :action="uploadUrl" :show-file-list="false" :headers="{'ba-user-token': userInfoStore.userInfo.token, server: true}" :on-success="handleAvatarSuccess">
                  <template v-if="form.avatar">
                    <el-avatar :size="60" :src="isHttpOrHttps(form.avatar)" />
                  </template>
                </el-upload>
              </el-form-item>
              <el-form-item label="用户名" prop="username">
                <el-input v-model="form.username" placeholder="请输入用户名"></el-input>
              </el-form-item>
              <el-form-item label="昵称" prop="nickname">
                <el-input v-model="form.nickname" placeholder="请输入昵称" />
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="form.gender">
                  <el-radio :value="0">男</el-radio>
                  <el-radio :value="1">女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="生日" prop="birthday">
                <el-date-picker
                    v-model="form.birthday"
                    type="date"
                    placeholder="请选择生日"
                    :editable="false"
                />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input type="number" v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="专业" prop="major">
                <el-input type="number" v-model="form.major" placeholder="请输入专业" />
              </el-form-item>
              <el-form-item label="毕业院校" prop="school">
                <el-input type="number" v-model="form.school" placeholder="请输入毕业院校" />
              </el-form-item>
              <el-form-item label="工作单位" prop="company">
                <el-input type="number" v-model="form.company" placeholder="请输入工作单位" />
              </el-form-item>
              <el-form-item label="">
                <div class="flex-1 flex justify-center">
                  <el-button type="primary" @click="confirm(formRef)">保存</el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .userinfo{
    margin: px2rem(20) 0;
  }
  .boxs{
    .sliders{
      width: px2rem(238);
    }
    .box{
      margin-left: px2rem(20);
      border-radius: px2rem(16);
      padding: px2rem(24) 0 px2rem(40);
      box-sizing: border-box;
      .title{
        color: #1F1F1F;
        font-size: px2rem(18);
        font-weight: 500;
        border-bottom: px2rem(1) solid #E9E9E9;
        padding: px2rem(0) px2rem(40) px2rem(16);
        box-sizing: border-box;
      }
      .form{
        padding: px2rem(30) px2rem(40);
        box-sizing: border-box;
        :deep(.el-form){
          .el-form-item{
            .el-form-item__content{
              .el-input{
                .el-input__wrapper{
                  background-color: #F7FAFE;
                  box-shadow: unset;
                }
              }
            }
          }
        }
        .el-button{
          width: px2rem(200);
          height: unset;
          line-height: 1;
          padding: px2rem(15) 0;
          box-sizing: border-box;
          border-radius: px2rem(8);
          margin-top: px2rem(70);
        }
      }
    }
  }
  .empty{
    height: px2rem(100);
  }
}
</style>