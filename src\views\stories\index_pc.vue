<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { headlineList } from '@/api/common.ts'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import paginate from "@/components/paginate.vue";
import adCard from '@/components/adCard.vue'
import storiesCard from "@/components/storiesCard.vue";
const limit = ref(10)
const menus = ref<any[]>([])
const menuCur = ref(0);
const filters = ref({
  category_id: '',
  headline_type: '',
  keyword: '',
  tag: ''
})
const options = ref({
  categories: [],
  channels: [],
  popularTags: []
})
const headlineTypes = ref<any[]>([])
const page = ref(1)
const total = ref(0)
const list = ref<any[]>([])

const getHeadlineList = () => {
  headlineList({
    ...filters.value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.pagination.total
    list.value = res.data.list
    options.value = res.data.options
    headlineTypes.value = res.data.headlineTypes || []

    // 动态生成菜单：全部 + API返回的分类
    menus.value = [
      { id: '', name: '全部' },
      ...(res.data.headlineTypes || [])
    ]
  })
}
const changePage = (e: number) => {
  page.value = e
  getHeadlineList()
}
const handleSearch = () => {
  page.value = 1
  getHeadlineList()
}
const changeChannel = (item: any) => {
  // 直接使用菜单项的id作为筛选条件
  filters.value['headline_type'] = item.id
  handleSearch()
}

onMounted(() => {
  getHeadlineList()
})
</script>

<template>
  <div class="main">
    <Header />
    <div class="items">
      <div class="container flex justify-between">
        <div class="left bg-white flex-1">
          <div class="menus flex align-center">
            <div class="menu cursor-pointer" :class="{active: menuCur === index}" v-for="(item, index) in menus" :key="index" @click="menuCur = index; changeChannel(item)">
              <span>{{item.name}}</span>
            </div>
          </div>
          <div class="items">
            <storiesCard :info="item" v-for="(item, index) in list" :key="index" />
          </div>
        </div>
        <div class="right">
          <adCard />
        </div>
      </div>
    </div>
    <div class="paginates">
      <div class="container">
        <paginate :limit="limit" :total="total" @change="changePage" />
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .items{
    margin-top: px2rem(24);
    .container{
      .left{
        box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
        border-radius: px2rem(24);
        padding: px2rem(20);
        box-sizing:  border-box;
        .menus{
          margin: px2rem(8) 0 px2rem(25);
          padding-bottom: px2rem(28);
          border-bottom: px2rem(1) solid #F7F8F9;
          .menu{
            color: #1F1F1F;
            font-size: px2rem(20);
            line-height: 1;
            font-weight: bold;
            &+.menu{
              margin-left: px2rem(40);
            }
            &.active{
              color: $maincolor;
            }
          }
        }
        .items{
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          grid-gap: px2rem(20);
        }
      }
      .right{
        margin-left: px2rem(24);
        width: px2rem(300);
      }
    }
  }
  .paginates{
    margin: px2rem(40) 0 px2rem(100);
  }
}
</style>