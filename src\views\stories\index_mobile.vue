<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { headlineList } from '@/api/common.ts'
import HeaderMobile from "@/components/headerMobile.vue";
import StoriesCardMobile from "@/components/storiesCardMobile.vue";
import paginate from "@/components/paginateMobile.vue";
const limit = ref(10)
const menus = ref<any[]>([])
const menuCur = ref(0);
const pageInfo = ref({
  title: '',
  subtitle: '',
})
const filters = ref({
  category_id: '',
  headline_type: '',
  keyword: '',
  tag: ''
})
const options = ref({
  categories: [],
  channels: [],
  popularTags: []
})
const headlineTypes = ref<any[]>([])
const page = ref(1)
const total = ref(0)
const list = ref([])
const getHeadlineList = () => {
  headlineList({
    ...filters.value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.pagination.total
    list.value = res.data.list
    options.value = res.data.options
    headlineTypes.value = res.data.headlineTypes || []

    // 动态生成菜单：全部 + API返回的分类
    menus.value = [
      { id: '', name: '全部' },
      ...(res.data.headlineTypes || [])
    ]
  })
}
const changePage = (e: number) => {
  page.value = e
  getHeadlineList()
}
const handleSearch = () => {
  page.value = 1
  getHeadlineList()
}
const changeChannel = (item: any) => {
  // 直接使用菜单项的id作为筛选条件
  filters.value['headline_type'] = item.id
  handleSearch()
}
onMounted(() => {
  getHeadlineList()
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="menus flex align-center bg-white">
      <div class="menu cursor-pointer" :class="{active: menuCur === index}" v-for="(item, index) in menus" :key="index" @click="menuCur = index; changeChannel(item)">
        <span>{{item.name}}</span>
      </div>
    </div>
    <div class="items auto flex-1">
      <div class="list">
        <StoriesCardMobile :info="item" v-for="(item, index) in list" :key="index" />
      </div>
    </div>
    <div class="paginates" v-if="total > limit">
      <paginate :limit="limit" :total="total" @change="changePage" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #FDFDFF;
  height: 100vh;
  .menus{
    overflow-x: auto;
    flex-wrap: nowrap;
    border-bottom: 1px solid #F7F8F9;
    margin: 0 15px 0;
    box-sizing: border-box;
    .menu{
      color: #535871;
      font-size: 14px;
      font-weight: 500;
      border-bottom: 2px solid transparent;
      display: inline-block;
      flex: 0 0 auto;
      &+.menu{
        margin-left: 16px;
      }
      &.active{
        color: $maincolor;
      }
    }
  }
  .items{
    padding: 12px 15px 0;
    box-sizing: border-box;
    .list{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px;
      grid-auto-rows: min-content;
    }
  }
  .paginates{
    margin: 10px 0;
  }
}
</style>