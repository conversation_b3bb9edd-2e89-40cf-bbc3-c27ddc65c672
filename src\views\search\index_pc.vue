<script setup lang="ts">
import {ref, onMounted, watch, nextTick} from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import serviceCard from "@/components/serviceCard.vue";
import expertCard from "@/components/expertCard.vue";
import journalCard from '@/components/journalCard.vue';
import storiesCard from "@/components/storiesCard.vue";
import paginate from "@/components/paginate.vue";
import { useRoute, useRouter } from 'vue-router'
import {commonSerach} from "@/api/common.ts";
const route = useRoute();
const router = useRouter();
const tabs = ref([
  {name: '全部', value: ''},
  {name: '学术科研服务', value: 'SERVICE'},
  {name: '专家智库', value: 'EXPERT'},
  {name: '学术期刊', value: 'JOURNAL'},
  {name: '学术头条', value: 'HEADLINE'},
])
const tabCur = ref(0)
const total = ref(0)
const page = ref(1)
const limit = ref(10)
const list = ref([])

const getList = () => {
  if (!route.query.keyword) {
    total.value = 0
    list.value = []
    return
  }
  commonSerach({
    page: page.value,
    keyword: route.query.keyword,
    type: tabs.value[tabCur.value].value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getList()
}
const handleSearch = (e: string) => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      keyword: e
    }
  })
}
watch(tabCur, (val) => {
  page.value = 1
  getList()
})
watch(
  () => route.query.keyword,
  () => {
    page.value = 1
    getList()
  }
)
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="main">
    <Header @search="handleSearch" />
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="box flex-1">
          <div class="tabs flex align-center">
            <div class="tab cursor-pointer" :class="{active: tabCur === index}" @click="tabCur = index" v-for="(item, index) in tabs" :key="index">
              <span>{{item.name}}</span>
            </div>
          </div>
          <div class="items">
            <div class="part" v-for="(item, index) in list" :key="index">
              <template v-if="item.content_type === 'SERVICE'">
                <serviceCard :info="item" />
              </template>
              <template v-else-if="item.content_type === 'EXPERT'">
                <expertCard :info="item" />
              </template>
              <template v-else-if="item.content_type === 'JOURNAL'">
                <journalCard :info="item" />
              </template>
              <template v-else-if="item.content_type === 'HEADLINE'">
                <storiesCard :info="item" />
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="paginates">
      <div class="container">
        <paginate :limit="limit" :total="total" @change="changePage" />
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .boxs{
    .box{
      .tabs{
        margin: px2rem(40) 0;
        .tab{
          color: #535871;
          font-size: px2rem(18);
          line-height: 1;
          padding: px2rem(10) px2rem(36);
          box-sizing:  border-box;
          border-radius: px2rem(28);
          &.active{
            background-color: $maincolor;
            color: #fff;
          }
        }
      }
      .items{
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: px2rem(40) px2rem(20);
      }
    }
  }
  .paginates{
    margin: px2rem(40) 0 px2rem(100);
  }
}
</style>