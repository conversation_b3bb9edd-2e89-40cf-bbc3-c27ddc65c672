import Axios from './axios';

export const initialization = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/index`,
        params: data,
    })
};

export const serviceList = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/serviceList`,
        params: data,
    })
};

export const expertList = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/expertList`,
        params: data,
    })
};

export const journalList = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/journalList`,
        params: data,
    })
};

export const headlineList = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/headlineList`,
        params: data,
    })
};

export const contentDetail = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/contentDetail`,
        params: data,
    })
};

export const commonSerach = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/commonserach`,
        params: data,
    })
};

export const upload = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/ajax/upload`,
        data: data,
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    })
};

export const profile = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/account/profile`,
        data: data
    })
};

export const getSubjectOptions = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/index/getSubjectOptions`,
        params: data
    })
};

export const toggleCollect = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/Index/toggleCollect`,
        data: data
    })
};

export const toggleLike = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/Index/toggleLike`,
        data: data
    })
};

export const myCollects = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/User/myCollects`,
        params: data
    })
};

export const myLikes = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/User/myLikes`,
        params: data
    })
};

export const myOrders = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/User/myOrders`,
        params: data
    })
};

export const cancelOrder = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/User/cancelOrder`,
        data: data
    })
};

export const createOrderAndPay = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/User/createOrderAndPay`,
        data: data
    })
};

export const createOrderAndPayWap = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/User/createOrderAndPayWap`,
        data: data
    })
};

export const checkPayStatus = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/User/checkPayStatus`,
        params: data
    }, {
        loading: false
    })
};

export const orderDetail = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/User/orderDetail`,
        params: data
    })
};
export const submitRefund = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/User/submitRefund`,
        data: data
    })
};

// 咨询相关API
export const createConsultation = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/User/createConsultation`,
        data: data
    })
};

export const myConsultations = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/User/myConsultations`,
        params: data
    })
};

// 获取验证码
export const getCaptcha = (id: string): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/common/captcha`,
        params: { id },
        responseType: 'blob'  // 因为返回的是图片
    }, {
        loading: false  // 不显示loading
    })
};