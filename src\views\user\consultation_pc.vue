<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import userInfoCard from "@/components/userInfoCard.vue";
import userSliderCard from "@/components/userSliderCard.vue";
import paginate from "@/components/paginate.vue";
import { myConsultations } from '@/api/common.ts'

// 定义咨询项类型
interface ConsultationItem {
  id: number
  name: string
  tel: string
  email: string
  content_id: number
  content_title: string
  cover_image: string
  price: number
  specialization: string
  status: string
  status_text: string
  create_time: string
  update_time?: string  // 更新时间
  remark?: string  // 回复内容
}

const tabs = ref([
  {name: '全部咨询', value: ''},
  {name: '待处理', value: '0'},
  {name: '已完成', value: '1'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref<ConsultationItem[]>([])
const total = ref(0)

// 详情弹窗相关
const dialogDetailVisible = ref(false)
const currentItem = ref<ConsultationItem | null>(null)

const getMyConsultations = () => {
  myConsultations({
    status: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    total.value = res.data.total
    list.value = res.data.list
  })
}

const changePage = (e: number) => {
  page.value = e
  getMyConsultations()
}

// 查看详情
const viewDetail = (item: ConsultationItem) => {
  currentItem.value = item
  dialogDetailVisible.value = true
}

const getStatusColor = (status: string) => {
  switch (status) {
    case '0':
      return '#F56C6C'  // 待处理
    case '1':
      return '#67C23A'  // 已完成
    default:
      return '#409EFF'
  }
}

watch(tabCur, () => {
  page.value = 1
  list.value = []
  getMyConsultations()
}, {
  deep: true
})

onMounted(() => {
  getMyConsultations()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="userinfo">
      <div class="container">
        <userInfoCard />
      </div>
    </div>
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="sliders">
          <userSliderCard />
        </div>
        <div class="box bg-white flex-1">
          <div class="tabs flex align-center">
            <div class="tab cursor-pointer" :class="{active: tabCur === index}" @click="tabCur = index"
                 v-for="(item, index) in tabs" :key="index">
              <span>{{ item.name }}</span>
            </div>
          </div>
          <div class="items">
            <div class="item flex" v-for="(item, index) in list" :key="index">
              <div class="img" style="width: 120px !important; height: 80px !important; overflow: hidden; flex-shrink: 0; border-radius: 8px; margin-right: 16px;">
                <img :src="item.cover_image" :alt="item.content_title" style="width: 100% !important; height: 100% !important; object-fit: cover !important; display: block !important;">
              </div>
              <div class="info flex-1 position-relative">
                <div class="title flex align-center justify-between">
                  <div class="name">
                    <span>{{ item.content_title }}</span>
                  </div>
                  <div class="status" :style="{color: getStatusColor(item.status)}">
                    <span>{{ item.status_text }}</span>
                  </div>
                </div>
                <div class="contact-info">
                  <div class="contact-item">
                    <span class="label">联系人：</span>
                    <span>{{ item.name }}</span>
                    <span class="separator">|</span>
                    <span class="label">电话：</span>
                    <span>{{ item.tel }}</span>
                    <span class="separator">|</span>
                    <span class="label">时间：</span>
                    <span>{{ item.create_time }}</span>
                  </div>
                </div>
                <div class="tools position-absolute">
                  <template v-if="item.status === '0'">
                    <el-button type="primary" @click="viewDetail(item)">查看详情</el-button>
                  </template>
                  <template v-if="item.status === '1'">
                    <el-button @click="viewDetail(item)">查看回复</el-button>
                  </template>
                </div>
              </div>
            </div>
            <div class="empty-state" v-if="list.length === 0">
              <div class="empty-text">暂无咨询记录</div>
            </div>
          </div>
          <div class="paginates" v-if="total > 0">
            <paginate :limit="limit" :total="total" @change="changePage"/>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
  </div>

  <!-- 详情弹窗 -->
  <el-dialog
    v-model="dialogDetailVisible"
    title="咨询详情"
    width="800"
    class="buyFormPop"
    :show-close="false"
  >
    <div class="buy-form">
      <div class="consultation-detail" v-if="currentItem">
        <!-- 基本信息行 -->
        <div class="info-row">
          <div class="detail-item half">
            <div class="detail-label">咨询内容</div>
            <div class="detail-value">{{ currentItem.content_title }}</div>
          </div>
          <div class="detail-item half">
            <div class="detail-label">状态</div>
            <div class="detail-value">
              <span :style="{ color: getStatusColor(currentItem.status) }">
                {{ currentItem.status_text }}
              </span>
            </div>
          </div>
        </div>

        <!-- 联系信息行 -->
        <div class="info-row">
          <div class="detail-item half">
            <div class="detail-label">联系人</div>
            <div class="detail-value">{{ currentItem.name }}</div>
          </div>
          <div class="detail-item half">
            <div class="detail-label">联系电话</div>
            <div class="detail-value">{{ currentItem.tel }}</div>
          </div>
        </div>

        <!-- 邮箱和专业方向行 -->
        <div class="info-row">
          <div class="detail-item half">
            <div class="detail-label">邮箱</div>
            <div class="detail-value">{{ currentItem.email || '未填写' }}</div>
          </div>
          <div class="detail-item half" v-if="currentItem.specialization">
            <div class="detail-label">专业方向</div>
            <div class="detail-value">{{ currentItem.specialization }}</div>
          </div>
        </div>

        <!-- 时间信息行 -->
        <div class="info-row">
          <div class="detail-item half">
            <div class="detail-label">创建时间</div>
            <div class="detail-value">{{ currentItem.create_time }}</div>
          </div>
          <div class="detail-item half">
            <div class="detail-label">更新时间</div>
            <div class="detail-value">{{ currentItem.update_time || currentItem.create_time }}</div>
          </div>
        </div>

        <!-- 服务封面 -->
        <div class="detail-item">
          <div class="detail-label">服务封面</div>
          <div class="detail-value">
            <img :src="currentItem.cover_image" :alt="currentItem.content_title" class="cover-image">
          </div>
        </div>

        <!-- 回复内容 -->
        <div class="detail-item">
          <div class="detail-label">回复内容</div>
          <div class="detail-value">
            <div class="remark-content" v-if="currentItem.remark">
              {{ currentItem.remark }}
            </div>
            <div class="remark-empty" v-else>
              暂无回复
            </div>
          </div>
        </div>

        <div class="detail-actions">
          <el-button type="primary" @click="dialogDetailVisible = false">关闭</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  
  .userinfo {
    margin: px2rem(20) 0;
  }
  
  .boxs {
    .sliders {
      width: px2rem(238);
    }
    
    .box {
      margin-left: px2rem(20);
      border-radius: px2rem(16);
      
      .tabs {
        padding: px2rem(24) px2rem(40) px2rem(11);
        box-sizing: border-box;
        border-bottom: px2rem(1) solid #E9E9E9;
        
        .tab {
          color: #535871;
          font-size: px2rem(18);
          border-bottom: px2rem(3) solid transparent;
          line-height: 1;
          padding-bottom: px2rem(5);
          
          & + .tab {
            margin-left: px2rem(50);
          }
          
          &.active {
            color: $maincolor;
            border-color: $maincolor;
          }
        }
      }

      .items {
        padding: px2rem(30) px2rem(40);
        box-sizing: border-box;

        .item {
          background-color: #F4F6F9;
          padding: px2rem(16);
          box-sizing: border-box;
          border-radius: px2rem(8);
          min-height: px2rem(100);

          & + .item {
            margin-top: px2rem(16);
          }

          .img {
            width: px2rem(120) !important;
            height: px2rem(80) !important;
            border-radius: px2rem(8);
            margin-right: px2rem(16);
            overflow: hidden;
            flex-shrink: 0;

            img {
              width: 100% !important;
              height: 100% !important;
              object-fit: cover !important;
              display: block !important;
            }
          }

          .info {
            .title {
              font-weight: 500;

              .name {
                font-size: px2rem(22);
              }

              .status {
                font-size: px2rem(18);
              }
            }

            .contact-info {
              margin-top: px2rem(12);

              .contact-item {
                display: flex;
                align-items: center;
                font-size: px2rem(14);
                color: #666;

                .label {
                  color: #999;
                  margin-right: px2rem(6);
                }

                .separator {
                  margin: 0 px2rem(12);
                  color: #ddd;
                }
              }
            }

            .tools {
              right: 0;
              bottom: 0;

              .el-button {
                border-radius: px2rem(23);
                border: 0;
                font-size: px2rem(16);

                &.el-button--primary {
                  background-color: #409EFF;
                  border-color: #409EFF;
                  color: #fff;

                  &:hover {
                    background-color: #66B1FF;
                    border-color: #66B1FF;
                  }
                }
              }
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: px2rem(60) 0;

          .empty-text {
            font-size: px2rem(16);
            color: #999;
          }
        }
      }
    }
  }
  
  .empty {
    height: px2rem(100);
  }
}

// 详情弹窗样式
:deep(.consultation-detail-dialog) {
  .el-dialog__body {
    padding: px2rem(20) px2rem(30);
  }

  .detail-content {
    .detail-item {
      display: flex;
      margin-bottom: px2rem(16);

      .detail-label {
        min-width: px2rem(100);
        color: #666;
        font-size: px2rem(14);
        font-weight: 500;
      }

      .detail-value {
        flex: 1;
        color: #333;
        font-size: px2rem(14);
        word-break: break-all;

        .cover-image {
          width: px2rem(120);
          height: px2rem(80);
          border-radius: px2rem(8);
          object-fit: cover;
          border: px2rem(1) solid #e0e0e0;
        }

        .remark-textarea {
          :deep(.el-textarea__inner) {
            background-color: #f9f9f9;
            border: px2rem(1) solid #e0e0e0;
            color: #333;
            resize: none;
          }
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .dialog-footer {
    text-align: center;
  }
}

// 强制控制图片样式
.main .boxs .box .items .item .img {
  width: px2rem(120) !important;
  height: px2rem(80) !important;

  img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }
}

// 购买弹窗样式
.buy-form {
  padding: 0 px2rem(70);
  box-sizing: border-box;
}

// 详情弹窗样式 - 使用购买弹窗样式
.consultation-detail {
  .info-row {
    display: flex;
    gap: px2rem(20);
    margin-bottom: px2rem(20);

    .detail-item.half {
      flex: 1;
      margin-bottom: 0;
    }
  }

  .detail-item {
    display: flex;
    margin-bottom: px2rem(20);
    align-items: flex-start;

    .detail-label {
      min-width: px2rem(100);
      color: #535871;
      font-size: px2rem(16);
      padding-right: px2rem(15);
      height: px2rem(35);
      line-height: px2rem(35);
      font-weight: 500;
    }

    .detail-value {
      flex: 1;
      font-size: px2rem(16);
      color: #333;
      word-break: break-all;
      line-height: px2rem(35);

      .cover-image {
        max-width: px2rem(150);
        height: auto;
        border-radius: px2rem(8);
        object-fit: cover;
        border: px2rem(1) solid #e0e0e0;
        line-height: 1;
      }

      .remark-content {
        background-color: #F7FAFE;
        padding: px2rem(12);
        border-radius: px2rem(8);
        line-height: 1.5;
        min-height: px2rem(60);
        font-size: px2rem(14);
      }

      .remark-empty {
        color: #999;
        font-style: italic;
        font-size: px2rem(14);
      }

      span {
        font-weight: 500;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-actions {
    margin-top: px2rem(30);
    text-align: center;

    .el-button {
      width: px2rem(150);
      height: px2rem(45);
      border-radius: px2rem(8);
      font-size: px2rem(16);
    }
  }
}
</style>
