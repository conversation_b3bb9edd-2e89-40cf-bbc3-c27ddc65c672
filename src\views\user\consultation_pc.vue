<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import userInfoCard from "@/components/userInfoCard.vue";
import userSliderCard from "@/components/userSliderCard.vue";
import paginate from "@/components/paginate.vue";
// import { myConsultations } from '@/api/common.ts'

const tabs = ref([
  {name: '全部咨询', value: ''},
  {name: '待处理', value: 'pending'},
  {name: '已完成', value: 'completed'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref([])
const total = ref(0)

// 模拟数据，实际应该调用API
const mockData = [
  {
    id: 1,
    title: '关于论文写作的问题',
    content: '请问如何提高论文的学术水平？',
    status: 'pending',
    status_text: '待处理',
    created_at: '2024-01-15 10:30:00',
    expert_name: '张教授',
    cover_image: 'https://via.placeholder.com/158x100',
    amount: '299.00'
  },
  {
    id: 2,
    title: '研究方法咨询',
    content: '定量研究和定性研究的区别是什么？',
    status: 'completed',
    status_text: '已完成',
    created_at: '2024-01-14 14:20:00',
    expert_name: '李教授',
    cover_image: 'https://via.placeholder.com/158x100',
    amount: '199.00'
  }
]

const getMyConsultations = () => {
  // 模拟API调用
  // myConsultations({
  //   status: tabs.value[tabCur.value].value,
  //   page: page.value,
  //   limit: limit.value
  // }).then(res => {
  //   total.value = res.data.total
  //   list.value = res.data.list
  // })
  
  // 使用模拟数据
  const filteredData = tabs.value[tabCur.value].value 
    ? mockData.filter(item => item.status === tabs.value[tabCur.value].value)
    : mockData
  
  total.value = filteredData.length
  list.value = filteredData
}

const changePage = (e: number) => {
  page.value = e
  getMyConsultations()
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return '#F56C6C'
    case 'completed':
      return '#67C23A'
    default:
      return '#409EFF'
  }
}

watch(tabCur, (val) => {
  page.value = 1
  list.value = []
  getMyConsultations()
}, {
  deep: true
})

onMounted(() => {
  getMyConsultations()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="userinfo">
      <div class="container">
        <userInfoCard />
      </div>
    </div>
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="sliders">
          <userSliderCard />
        </div>
        <div class="box bg-white flex-1">
          <div class="tabs flex align-center">
            <div class="tab cursor-pointer" :class="{active: tabCur === index}" @click="tabCur = index"
                 v-for="(item, index) in tabs" :key="index">
              <span>{{ item.name }}</span>
            </div>
          </div>
          <div class="items">
            <div class="item flex justify-between" v-for="(item, index) in list" :key="index">
              <div class="img">
                <img :src="item.cover_image" :alt="item.title">
              </div>
              <div class="info flex-1 position-relative">
                <div class="title flex align-center justify-between">
                  <div class="name">
                    <span>{{ item.title }}</span>
                  </div>
                  <div class="status" :style="{color: getStatusColor(item.status)}">
                    <span>{{ item.status_text }}</span>
                  </div>
                </div>
                <div class="money">
                  <span>¥{{ item.amount }}</span>
                </div>
                <div class="tools position-absolute">
                  <template v-if="item.status === 'pending'">
                    <el-button type="primary">查看详情</el-button>
                  </template>
                  <template v-if="item.status === 'completed'">
                    <el-button>查看回复</el-button>
                  </template>
                </div>
              </div>
            </div>
            <div class="empty-state" v-if="list.length === 0">
              <div class="empty-text">暂无咨询记录</div>
            </div>
          </div>
          <div class="paginates" v-if="total > 0">
            <paginate :limit="limit" :total="total" @change="changePage"/>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  
  .userinfo {
    margin: px2rem(20) 0;
  }
  
  .boxs {
    .sliders {
      width: px2rem(238);
    }
    
    .box {
      margin-left: px2rem(20);
      border-radius: px2rem(16);
      
      .tabs {
        padding: px2rem(24) px2rem(40) px2rem(11);
        box-sizing: border-box;
        border-bottom: px2rem(1) solid #E9E9E9;
        
        .tab {
          color: #535871;
          font-size: px2rem(18);
          border-bottom: px2rem(3) solid transparent;
          line-height: 1;
          padding-bottom: px2rem(5);
          
          & + .tab {
            margin-left: px2rem(50);
          }
          
          &.active {
            color: $maincolor;
            border-color: $maincolor;
          }
        }
      }
      
      .items {
        padding: px2rem(30) px2rem(40);
        box-sizing: border-box;

        .item {
          background-color: #F4F6F9;
          padding: px2rem(20);
          box-sizing: border-box;
          border-radius: px2rem(8);

          & + .item {
            margin-top: px2rem(20);
          }

          .img {
            width: px2rem(158);
            height: px2rem(100);
            border-radius: px2rem(10);
            margin-right: px2rem(20);

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .info {
            .title {
              font-weight: 500;

              .name {
                font-size: px2rem(22);
              }

              .status {
                font-size: px2rem(18);
              }
            }

            .money {
              font-size: px2rem(20);
              color: #525252;
              margin-top: px2rem(12);
            }

            .tools {
              right: 0;
              bottom: 0;

              .el-button {
                border-radius: px2rem(23);
                border: 0;
                font-size: px2rem(16);

                &.el-button--primary {
                  background-color: #409EFF;
                  border-color: #409EFF;
                  color: #fff;

                  &:hover {
                    background-color: #66B1FF;
                    border-color: #66B1FF;
                  }
                }
              }
            }
          }
        }
        
        .empty-state {
          text-align: center;
          padding: px2rem(60) 0;
          
          .empty-text {
            font-size: px2rem(16);
            color: #999;
          }
        }
      }
    }
  }
  
  .empty {
    height: px2rem(100);
  }
}
</style>
