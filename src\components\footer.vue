<script setup lang="ts">
import {getAssetsFile, isHttpOrHttps} from "@/utils/utils.ts";
import {safePush} from "@/utils/router.ts";
import { useSystemStore } from '@/store/system.ts'
const systemStore = useSystemStore()
</script>

<template>
  <footer>
    <div class="container">
      <div class="first flex justify-between">
        <div class="logo cursor-pointer" @click="safePush('/')">
          <el-image :src="getAssetsFile('common/logo_1.png')"></el-image>
        </div>
        <div class="info flex-1 flex justify-between">
          <div class="items flex flex-column">
            <p>业务板块</p>
            <el-link underline="never">研知AI</el-link>
            <el-link underline="never">学术科研服务</el-link>
            <el-link underline="never">论文辅导SVIP</el-link>
            <el-link underline="never">论文发表SVIP</el-link>
          </div>
          <div class="items flex flex-column">
            <p>关于我们</p>
            <el-link underline="never" @click="safePush({path: '/cate', query: {name: item.title}})" v-for="(item, index) in systemStore.hiddenNav">{{item.title}}</el-link>
            <el-link underline="never" @click="safePush('/service')">学术头条</el-link>
          </div>
          <div class="items flex flex-column">
            <p>更多</p>
            <el-link underline="never">电话：{{systemStore.base_tel}}</el-link>
            <el-link underline="never">邮箱：{{systemStore.base_emal}}</el-link>
            <el-link underline="never">工作日：{{systemStore.base_jobtime}}</el-link>
          </div>
        </div>
        <div class="qrcode">
          <div class="qr">
            <el-image :src="isHttpOrHttps(systemStore.wx_qrcode)"></el-image>
          </div>
          <div class="tip text-center">
            <span>公众号</span>
          </div>
        </div>
      </div>
      <div class="second flex align-center justify-center">
        <el-link underline="never">研知有术</el-link>
        <span>|</span>
        <el-link underline="never">用户协议</el-link>
        <span>|</span>
        <el-link underline="never">使用规范</el-link>
        <span>|</span>
        <el-link underline="never">隐私政策</el-link>
      </div>
    </div>
  </footer>
</template>

<style scoped lang="scss">
footer {
  background-color: #303035;
  padding: px2rem(30) 0 px2rem(40);
  box-sizing: border-box;

  .container {
    .first {
      .logo {
        .el-image {
          width: px2rem(197);
          height: px2rem(66);
        }
      }

      .qrcode {
        .qr {
          width: px2rem(140);
          height: px2rem(140);
          background-color: $maincolor;
        }

        .tip {
          margin-top: px2rem(16);
          color: #909090;
          font-size: px2rem(18);
        }
      }

      .info {
        margin: 0 px2rem(85) 0 px2rem(80);

        .items {
          text-align: left;

          p {
            color: #939393;
            font-size: px2rem(18);
          }

          .el-link {
            color: #939393;
            display: inline-block;
            margin-top: px2rem(22);
            font-weight: normal;
            font-size: px2rem(16);
          }
        }
      }
    }

    .second {
      margin-top: px2rem(80);
      color: #A2A2A2;

      span {
        padding: 0 px2rem(5);
        box-sizing: border-box;
      }

      .el-link {
        color: #A2A2A2;
        font-size: px2rem(14);
      }
    }
  }
}
</style>