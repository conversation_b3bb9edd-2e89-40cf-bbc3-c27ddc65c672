<script setup lang="ts">

import { useUserInfoStore } from '@/store/userInfo.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
const userInfoStore = useUserInfoStore()
</script>

<template>
  <div class="info flex align-center">
    <div class="avatar hidden bg-white">
      <img :src="isHttpOrHttps(userInfoStore.userInfo.avatar)" alt="">
    </div>
    <div class="nickname">
      <span>{{userInfoStore.userInfo.nickname}}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.info{
  .avatar{
    width: 48px;
    height: 48px;
    border-radius: 50%;
    padding: 2px;
    box-sizing: border-box;
    img{
      width: 100%;
      border-radius: 50%;
    }
  }
  .nickname{
    margin-left: 10px;
    font-size: 16px;
    color: #000;
    font-weight: 500;
  }
}
</style>