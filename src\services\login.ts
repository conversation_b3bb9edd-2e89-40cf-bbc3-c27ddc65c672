import { createApp } from 'vue'
import Login from '@/components/login.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

class LoginService {
  private static instance: LoginService
  private container: HTMLElement | null = null
  private app: any = null
  private componentInstance: any = null

  private constructor() {}

  static getInstance() {
    if (!LoginService.instance) {
      LoginService.instance = new LoginService()
    }
    return LoginService.instance
  }

  show() {
    if (!this.container) {
      this.container = document.createElement('div')
      document.body.appendChild(this.container)
      this.app = createApp(Login)
      this.app.use(ElementPlus)
      this.componentInstance = this.app.mount(this.container)
    }
    this.componentInstance.show()
  }

  hide() {
    if (this.componentInstance) {
      this.componentInstance.hide()
    }
  }

  toggle() {
    if (this.container) {
      this.hide()
    } else {
      this.show()
    }
  }
}

export const loginService = LoginService.getInstance() 