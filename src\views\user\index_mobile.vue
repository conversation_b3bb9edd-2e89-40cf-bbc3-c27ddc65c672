<script setup lang="ts">
import {onMounted, ref, watch} from 'vue'
import HeaderTitle from "@/components/headerTitle.vue";
import {backPage, safePush} from "@/utils/router.ts";
import UserInfoCardMobile from "@/components/userInfoCardMobile.vue";
import {getAssetsFile} from "@/utils/utils.ts";
import type { OrderItem } from '@/types/user.ts'
import {ElMessage, ElMessageBox} from "element-plus";
import {cancelOrder, checkPayStatus, myOrders} from "@/api/common.ts";
import paginateMobile from "@/components/paginateMobile.vue";

let timeObj = null
const orderInfo = ref({})
const menus = ref([
  {label: '收藏', url: '/user/collect', icon: getAssetsFile('common_mobile/icon_user_collect.png')},
  {label: '资料', url: '/user/account', icon: getAssetsFile('common_mobile/icon_user_user.png')},
  {label: '设置', url: '/user/set', icon: getAssetsFile('common_mobile/icon_user_set.png')},
])
const tabs = ref([
  {name: '全部订单', value: ''},
  {name: '待付款', value: 'pending'},
  {name: '已付款', value: 'paid'},
  {name: '进行中', value: 'in_progress'},
  {name: '已完成', value: 'completed'},
  {name: '已取消', value: 'cancelled'},
  {name: '已退款', value: 'refunded'},
])
const tabCur = ref(0)
const limit = ref(10)
const page = ref(1)
const list = ref<OrderItem[]>([])
const total = ref(0)

const getStatusColor = (status: string) => {
  let color = '';
  switch (status) {
    case 'pending':
    case '待付款':
      color = '#26B84C';
      break;
    case 'paid':
    case 'in_progress':
    case '待完成':
      color = '#F1A92E';
      break;
    case 'completed':
    case '已完成':
      color = '#1F1F1F';
      break;
    case 'cancelled':
    case 'refunded':
    case '售后中':
      color = '#E5331B';
      break;
    default:
      color = '#1F1F1F';
      break;
  }
  return color;
}

const handleAfterSales = (item: OrderItem) => {
  safePush('/user/afterSales')
}

// 处理支付
const handlePayment = (item: OrderItem) => {
  orderInfo.value = item
  window.location.href = item.qr_code_url
  timeObj = setInterval(() => {
    checkOrders()
  }, 3000)
}

const checkOrders = () => {
  if (!orderInfo.value.id) {
    timeObj && clearInterval(timeObj)
    return
  }
  checkPayStatus({order_id: orderInfo.value.id}).then(res => {
    if (['paid', 'completed'].includes(res.data.status)) {
      // 已付款、已完成
      clearInterval(timeObj)
      ElMessage.success(res.msg)
      getMyOrders()
    } else if (['cancelled', 'refunded'].includes(res.data.status)) {
      // 已取消、已退款
      clearInterval(timeObj)
      ElMessage.error(res.msg)
      getMyOrders()
    }
  })
}

const handleCancel = (item: OrderItem) => {
  ElMessageBox.confirm('是否确认取消订单?')
    .then(() => {
      cancelOrder({
        order_id: item.id,
        reason: '用户取消订单'
      }).then((res) => {
        ElMessage.success(res.msg)
        getMyOrders()
      })
    })
    .catch(() => {

    })
}

const getMyOrders = () => {
  myOrders({
    status: tabs.value[tabCur.value].value,
    page: page.value,
    limit: limit.value,
  }).then(res=>{
    total.value = res.data.total
    list.value = res.data.list
  })
}

const changePage = (e: number) => {
  page.value = e
  getMyOrders()
}
const handleRefuse = (item: OrderItem) => {
  safePush({
    path: '/user/afterSales',
    query: {
      id: item.id
    }
  })
}

watch(tabCur, (val) => {
  page.value = 1
  list.value = []
  getMyOrders()
},{
  deep: true
})

onMounted(() => {
  getMyOrders()
})
</script>

<template>
  <div class="page flex flex-column hidden">
    <HeaderTitle @click="backPage"/>
    <div class="box flex-1 flex flex-column hidden">
      <UserInfoCardMobile/>
      <div class="card bg-white flex align-center justify-between">
        <div class="menu text-center" v-for="(item, index) in menus" :key="index" @click="safePush(item.url)">
          <img :src="item.icon" alt="">
          <div class="name">{{ item.label }}</div>
        </div>
      </div>
      <div class="tabs flex">
        <div class="tab text-center" :class="{active: tabCur === index}" @click="tabCur = index"
             v-for="(item, index) in tabs" :key="index">
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="items flex-1 auto">
        <div class="item bg-white flex justify-between" v-for="(item, index) in list" :key="index">
          <div class="img">
            <img :src="item.cover_image" :alt="item.service_title">
          </div>
          <div class="info flex-1">
            <div class="title flex align-center justify-between">
              <div class="name">
                <span>{{item.service_title}}</span>
              </div>
            </div>
            <div class="price flex justify-between">
              <div class="money">
                <span>¥{{item.total_amount}}</span>
              </div>
              <div class="right flex align-center">
                <div class="tools">
                  <template v-if="item.status === 'pending'">
                    <span @click="handleCancel(item)">取消订单</span>
                    <span @click="handlePayment(item)">立即支付</span>
                  </template>
                  <template v-else-if="['paid', 'in_progress'].includes(item.status)">
                    <span @click="handleRefuse(item)">申请退款</span>
                  </template>
                  <template v-if="item.status === 'completed'">
                    <span @click="handleAfterSales(item)">申请售后</span>
                  </template>
                  <template v-if="item.status === 'refunded'">
                    <span>取消售后</span>
                  </template>
                </div>
                <div class="status" :style="{color: getStatusColor(item.status)}">
                  <span>{{item.status_text}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="paginates">
        <paginateMobile :limit="limit" :total="total" @change="changePage" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  width: 100%;
  height: 100vh;
  background: url("@/assets/images/common_mobile/bg_user.png") no-repeat;
  background-size: cover;

  .box {
    padding: 20px 17px;
    box-sizing: border-box;

    .card {
      margin: 16px 0 20px;
      padding: 15px 39px 20px;
      box-sizing: border-box;
      box-shadow: 0 1px 6px 0 #ECEAF6;
      border-radius: 12px;

      .menu {
        img {
          width: 55px;
        }

        .name {
          font-size: 12px;
          color: #262729;
          font-weight: 500;
          margin-top: -15px;
        }
      }
    }

    .tabs {
      overflow-x: auto;
      flex-wrap: nowrap;
      width: 100%;
      margin-bottom: 9px;

      .tab {
        color: #535871;
        font-size: 14px;
        font-weight: 500;
        border-bottom: 2px solid transparent;
        display: inline-block;
        flex: 0 0 auto;

        & + .tab {
          margin-left: 16px;
        }

        &.active {
          color: $maincolor;
          border-color: $maincolor;
        }
      }
    }

    .items{
      .item{
        padding: 12px;
        box-sizing: border-box;
        border-radius: 12px;
        box-shadow: 0 1px 6px 0 #ECEAF6;
        &+.item{
          margin-top: 12px;
        }
        .img{
          width: 100px;
          height: 63px;
          border-radius: 5px;
          margin-right: 10px;
          img{
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .info{
          .title{
            font-weight: 500;
            .name{
              font-size: 16px;
            }

          }
          .price{
            margin-top: 19px;
            .money{
              font-size: 14px;
              color: #525252;
            }
            .right{
              .tools{
                font-size: 12px;
                color: #909090;
                span{
                  &+span{
                    padding-left: 16px;
                  }
                }
              }
              .status{
                margin-left: 16px;
                font-size: 12px;
              }
            }
          }


        }
      }
    }
    .paginates{
      margin-top: 10px;
    }
  }
}
</style>