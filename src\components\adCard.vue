<script setup lang="ts">
import { useSystemStore } from '@/store/system.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
const systemStore = useSystemStore()
</script>

<template>
  <div class="ad bg-white flex justify-between align-center">
    <div class="_left">
      <div class="title">
        <span>学术资源免费领取</span>
      </div>
      <div class="line"></div>
      <div class="desc">
        <span>加微信领取20G科研大礼包！更多众多热门</span>
      </div>
    </div>
    <div class="_right">
      <img :src="isHttpOrHttps(systemStore.kefu_qrcode)" alt="">
    </div>
  </div>
</template>

<style scoped lang="scss">
.ad{
  width: 100%;
  padding: px2rem(18);
  box-sizing:  border-box;
  box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
  border-radius: px2rem(8);
  ._left{
    .title{
      color: #EA7F46;
      font-size: px2rem(20);
      line-height: 1;
    }
    .line{
      background-color: #EA7F46;
      margin: px2rem(10) 0;
      width: px2rem(23);
      height: px2rem(3);
    }
    .desc{
      font-size: px2rem(14);
      color: #9098A1;
      line-height: px2rem(20);
    }
  }
  ._right{
    width: px2rem(90);
    height: px2rem(90);
    margin-left: px2rem(12);
    img{
      width: 100%;
    }
  }
}
</style>