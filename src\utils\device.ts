import { ref, onMounted, onUnmounted } from 'vue'

// 判断设备类型
export const isMobileDevice = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
    )
}

// 响应式判断，考虑窗口大小变化
export const useDevice = () => {
    const isMobile = ref(isMobileDevice())

    const checkScreen = () => {
        // 如果宽度小于768或移动设备，认为是移动端
        isMobile.value = window.innerWidth < 768 || isMobileDevice()
    }

    onMounted(() => {
        checkScreen()
        window.addEventListener('resize', checkScreen)
    })

    onUnmounted(() => {
        window.removeEventListener('resize', checkScreen)
    })

    return { isMobile }
}

// 直接导出一个判断函数
export const isMobile = () => {
    return window.innerWidth < 768 || isMobileDevice()
}