<script setup lang="ts">
import {Close} from "@element-plus/icons-vue";
import { useSystemStore } from '@/store/system'
const systemStore = useSystemStore()
</script>

<template>
  <div class="header flex justify-between align-center bg-white">
    <el-icon size="18" @click.stop="$emit('click')"><Close /></el-icon>
    <div class="title row1">
      <span>{{systemStore.site.siteName}}</span>
    </div>
    <div></div>
  </div>
</template>

<style scoped lang="scss">
.header{
  width: 100%;
  height: 54px;
  padding: 12px 15px;
  box-sizing: border-box;
  border-bottom: 1px solid #F5F7FA;
}
</style>