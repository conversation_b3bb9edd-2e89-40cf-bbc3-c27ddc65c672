<script setup lang="ts">
import { safePush } from "@/utils/router.ts";
import {isHttpOrHttps} from "@/utils/utils.ts";
const { info, isFlex, avatarSize } = defineProps({
  info: {
    type: Object,
    required: true,
  },
  isFlex: {
    type: Boolean,
    required: false,
    default: false,
  },
  avatarSize: {
    type: Number,
    required: false,
    default: 55
  }
});
</script>

<template>
  <div class="item hidden bg-white position-relative" :class="{flex: isFlex}" @click="safePush({path:'/expert/details',query:{id:info.id}})">
    <div class="avatar flex hidden" :style="{width: avatarSize+'px', height: avatarSize+'px'}">
      <el-avatar :size="55" :src="isHttpOrHttps(info.avatar_url)" fit="cover" />
    </div>
    <div class="info flex-1" :style="{marginLeft: isFlex ? '8px' : 0, marginTop: isFlex ? 0 : '8px'}">
      <div class="names flex align-center">
        <div class="name">
          <span v-html="info.title"></span>
        </div>
        <div class="tag position-absolute">
          <span>{{info.expert_number ?? '-'}}</span>
        </div>
      </div>
      <div class="job">
        <span>{{info.summary}}</span>
      </div>
      <div class="tags">
        <div class="tag" v-for="(item, index) in info.research_area?.split(',')" :key="index">
          <span>{{item}}</span>
        </div>
      </div>
      <div class="description row1">
        <span>{{info.academic_achievements}}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item{
  box-shadow: 0 1px 12px 0 rgba(0, 66, 190, 0.14);
  border-radius: 12px;
  padding: 12px 10px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;

  .avatar {
    border-radius: 50%;
    margin: 0 auto;
    img{
      width: 100%;
      height: 100%;
    }
  }

  .info{
    .names {
      .name {
        font-size: 14px;
        font-weight: 500;
      }

      .tag {
        top: 0;
        right: 0;
        padding: 2px 6px 2px 8px;
        box-sizing: border-box;
        background: linear-gradient(90deg, #F8F0DF 0%, #EFE3C8 100%);
        font-size: 12px;
        border-radius: 0 12px 0 12px;
        font-weight: 500;
        color: #452111;
      }
    }

    .job {
      color: #535871;
      font-size: 12px;
      font-weight: 600;
      margin: 4px 0 6px;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;

      .tag {
        padding: 2px 4px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #CCCCCC;
        font-size: 11px;
        color: #434864;
        margin-right: 4px;
        margin-bottom: 4px;
      }
    }

    .description {
      margin-top: 2px;
      color: #535871;
      font-size: 12px;
    }
  }

}
</style>