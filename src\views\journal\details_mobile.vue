<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Star, StarFilled } from '@element-plus/icons-vue'
import HeaderMobile from "@/components/headerMobile.vue";
import { contentDetail, toggleCollect, createConsultation, getCaptcha } from '@/api/common.ts'
import {ElMessage, type FormInstance} from "element-plus";
import { useRoute, useRouter } from 'vue-router'
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import ShareCardMobile from "@/components/shareCardMobile.vue";
import OtherCardMobile from "@/components/otherCardMobile.vue";
import ServiceCardMobile from "@/components/serviceCardMobile.vue";
import ExpertCardMobile from "@/components/expertCardMobile.vue";
import AboutCardMobile from "@/components/aboutCardMobile.vue";
import JournalCardMobile from "@/components/journalCardMobile.vue";
import { useSystemStore } from '@/store/system.ts'
import FriendCardMobile from "@/components/friendCardMobile.vue";
const systemStore = useSystemStore()
const route = useRoute();
const router = useRouter();
const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: []
})

// 咨询弹窗相关
const dialogConsultationVisible = ref(false)
const consultationFormRef = ref<FormInstance>()
const consultationFormRef2 = ref<FormInstance>()

// 咨询表单数据 - 按照数据库字段命名
const consultationForm = ref({
  content_id: '', // 服务ID
  name: '',       // 姓名
  tel: '',        // 电话
  email: '',      // 邮箱
  specialization: '', // 专业方向
  captcha: ''
})

// 验证码相关
const captchaId = ref('')
const captchaUrl = ref('')

// 咨询表单验证规则 - 按照数据库字段命名
const consultationRules = ref({
  name: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  tel: [
    { required: true, message: '请输入电话', trigger: 'blur' },
    { type: 'string', pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
  ],
  email: [
    {
      pattern: /^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: '请输入有效的邮箱地址',
      trigger: 'blur'
    }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '验证码长度为4-6位', trigger: 'blur' }
  ]
})
const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
    ElMessage.success(res.msg)
  })
}

// 生成验证码ID
const generateCaptchaId = () => {
  return 'captcha_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 刷新验证码
const refreshCaptcha = async () => {
  captchaId.value = generateCaptchaId()
  console.log('移动端刷新验证码ID:', captchaId.value)

  try {
    const response = await getCaptcha(captchaId.value)
    console.log('移动端验证码响应:', response)
    console.log('移动端响应数据类型:', typeof response.data)
    console.log('移动端响应数据:', response.data)

    // response.data 是blob数据
    const blob = response.data
    captchaUrl.value = URL.createObjectURL(blob)
    console.log('移动端验证码URL生成成功:', captchaUrl.value)
    console.log('移动端captchaUrl.value 当前值:', captchaUrl.value)
  } catch (error) {
    console.error('移动端获取验证码失败:', error)
    ElMessage.error('获取验证码失败，请重试')
  }
}

// 打开咨询弹窗
const handleConsultation = () => {
  dialogConsultationVisible.value = true
  consultationFormRef.value?.resetFields()
  // 设置服务ID
  consultationForm.value.content_id = (info.value.detail as any).id
  // 生成验证码
  refreshCaptcha()
}

// 提交咨询表单
const submitConsultation = async () => {
  const formEl1 = consultationFormRef.value
  const formEl2 = consultationFormRef2.value

  if (!formEl1 || !formEl2) return

  // 验证所有表单
  try {
    await formEl1.validate()
    await formEl2.validate()

    dialogConsultationVisible.value = false
    createConsultation({
      content_id: consultationForm.value.content_id, // 服务ID
      name: consultationForm.value.name,              // 姓名
      tel: consultationForm.value.tel,                // 电话
      email: consultationForm.value.email,            // 邮箱
      specialization: consultationForm.value.specialization, // 专业方向
      captchaId: captchaId.value,
      captcha: consultationForm.value.captcha
    }).then(res => {
      ElMessage.success(res.msg)
      // 重置表单
      formEl1.resetFields()
      formEl2.resetFields()
      // 刷新验证码
      refreshCaptcha()
      // 跳转到用户中心的咨询列表
      router.push('/user/consultation')
    }).catch(err => {
      ElMessage.error(err.msg)
      // 刷新验证码
      refreshCaptcha()
    })
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}
const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="flex-1 auto" style="overflow-x: hidden">
      <div class="card bg-white">
        <div class="box bg-white flex justify-between">
          <div class="left">
            <img :src="isHttpOrHttps(info.detail.cover_image)" alt="">
          </div>
          <div class="right flex-1">
            <div class="title flex align-center">
              <div class="name">
                <span>{{info.detail.title}}</span>
              </div>
            </div>
            <div class="descs">
              <div class="part flex align-center">
                <div class="name">
                  <span>学科领域</span>
                </div>
                <div class="value">
                  <span>{{info.detail.big_type_name + '-' + info.detail.small_type_name }}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>期刊类型</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_type ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>数据库</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_datatype ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>ISSN</span>
                </div>
                <div class="value">
                  <span>--</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>中科院</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_zky ?? '--'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>影响因子</span>
                </div>
                <div class="value">
                  <span>{{info.detail.impact_factor ?? '--'}}</span>
                </div>
              </div>
              <div class="part flex align-center">
                <div class="name">
                  <span>JCR</span>
                </div>
                <div class="value">
                  <span>{{info.detail.journal_jcr ?? '--'}}</span>
                </div>
              </div>
            </div>
            <div style="margin-top: 12px">
              <ShareCardMobile :title="info.detail.title" :image="isHttpOrHttps(info.detail.cover_image)" style="margin-top: 4px">
                <div class="tool">
                  <el-button type="primary" @click="handleConsultation">立即投稿</el-button>
                  <el-button color="#EEAF3D" :icon="info.detail?.is_collected ? StarFilled : Star" @click="handleCollect">收藏</el-button>
                </div>
              </ShareCardMobile>
            </div>
          </div>
        </div>
      </div>
      <div class="card bg-white">
        <div class="info">
          <div class="name-bar flex align-center">
            <img src="@/assets/images/common/icon_item.png" alt="">
            <span>期刊简介</span>
          </div>
          <div class="values" v-html="info.detail.summary"></div>
          <div class="name-bar flex align-center">
            <img src="@/assets/images/common/icon_item.png" alt="">
            <span>征稿主题</span>
          </div>
          <div class="values" v-html="info.detail.journal_data ?? '-'"></div>
          <div class="name-bar flex align-center">
            <img src="@/assets/images/common/icon_item.png" alt="">
            <span>参考周期</span>
          </div>
          <div class="values" v-html="info.detail.paper_requirements ?? '-'"></div>
          <div class="name-bar flex align-center">
            <img src="@/assets/images/common/icon_item.png" alt="">
            <span>投稿须知</span>
          </div>
          <div class="values" v-html="info.detail.submission_guidelines ?? '-'"></div>
          <div class="friends">
            <FriendCardMobile />
          </div>
        </div>
      </div>
      <div class="card">
        <OtherCardMobile title="相关推荐" :icon="getAssetsFile('common/icon_zan.png')" more-url="/journal">
          <div class="services">
            <JournalCardMobile :info="item" v-for="(item, index) in info.recommended" :key="index" />
          </div>
          <div class="about" v-if="info.related.services?.length">
            <aboutCardMobile title="热门服务" :icon="getAssetsFile('common/icon_service.png')" moreUrl="/service">
              <div class="items">
                <ServiceCardMobile :info="item" v-for="(item, index) in info.related.services" :key="index" />
              </div>
            </aboutCardMobile>
          </div>
          <div class="about" v-if="info.related.experts?.length">
            <aboutCardMobile title="相关专家" :icon="getAssetsFile('common/icon_zj.png')" moreUrl="/expert">
              <div class="items">
                <ExpertCardMobile :info="item" v-for="(item, index) in info.related.experts" :key="index" />
              </div>
            </aboutCardMobile>
          </div>
        </OtherCardMobile>
      </div>
    </div>

    <!-- 咨询弹窗 -->
    <el-dialog
        v-model="dialogConsultationVisible"
        width="90%"
        title="投稿申请"
        class="mobile-consultation-pop"
        :show-close="false"
    >
      <!-- 第一块：提示信息 -->
      <div class="tip-box bg-white">
        <div class="tip">请填写您的联系方式，我们的专业导师将在二十四小时内联系您。</div>
      </div>

      <!-- 第二块：表单信息 -->
      <div class="pay-box bg-white">
        <div class="title">联系信息</div>
        <el-form ref="consultationFormRef" :model="consultationForm" :rules="consultationRules" label-position="left" label-width="auto">
          <el-form-item label="联系人" prop="name">
            <el-input v-model="consultationForm.name" placeholder="请输入联系人" />
          </el-form-item>
          <el-form-item label="电话" prop="tel">
            <el-input v-model="consultationForm.tel" placeholder="请输入电话" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="consultationForm.email" placeholder="请输入邮箱（选填）" />
          </el-form-item>
          <el-form-item label="专业方向">
            <el-input v-model="consultationForm.specialization" placeholder="请输入专业方向（选填）" />
          </el-form-item>
        </el-form>
      </div>

      <!-- 第三块：验证码 -->
      <div class="captcha-box bg-white">
        <div class="title">验证码</div>
        <el-form ref="consultationFormRef2" :model="consultationForm" :rules="consultationRules" label-position="left" label-width="auto">
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="consultationForm.captcha"
                placeholder="请输入验证码"
                style="flex: 1; margin-right: 10px;"
              />
              <div
                class="captcha-image"
                @click="refreshCaptcha"
                style="width: 120px; height: 40px; border: 1px solid #dcdfe6; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; background: #f5f7fa;"
              >
                <img v-if="captchaUrl" :src="captchaUrl" alt="验证码" style="max-width: 100%; max-height: 100%;" />
                <span v-else style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">点击获取</span>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 第四块：操作按钮 -->
      <div class="btn-box bg-white">
        <el-button type="primary" @click="submitConsultation" style="width: 100%;">提交咨询</el-button>
        <el-button @click="dialogConsultationVisible = false" style="width: 100%; margin-top: 10px;">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.page {
  background: #F4F6F9;
  height: 100vh;
  .card {
    margin-top: 5px;
    .box{
      padding: 20px 15px;
      box-sizing:  border-box;
      .left{
        width: 60px;
        height: 80px;
        img{
          width: 100%;
        }
      }
      .right{
        margin-left: 16px;
        .title{
          .name{
            font-size: 18px;
            color: #2D2B32;
            line-height: 1;
            font-weight: 600;
          }
        }
        .descs{
          margin-top: 10px;
          .part{
            font-size: 12px;
            &+.part{
              margin-top: 5px;
            }
            .name{
              color: #93999A;
              width: 50px;
            }
            .value{
              color: #1F1F1F;
              margin-left: 10px;
            }
          }
        }
        .tool{
          .el-button{
            font-size: 14px;
            width: 98px;
            height: 32px;
            border-radius: 27px;
            &:last-child{
              background-color: transparent;
              color: #EEAF3D;
            }
          }
        }
      }
    }
    .info{
      padding: 20px 15px;
      box-sizing:  border-box;
      .name-bar{
        color: #191B1F;
        font-weight: 600;
        font-size: 16px;
        padding-bottom: 10px;
        box-sizing: border-box;
        img{
          width: 23px;
          height: 16px;
          margin-right: 10px;
        }
      }
      .values{
        color: #525252;
        font-size: 14px;
        line-height: 25px;
        margin-bottom: 43px;
      }
      .cards{
        background-color: #3B78C8;
        margin-top: 30px;
        padding: 9px;
        box-sizing: border-box;
        .parts{
          .part{
            padding: 28px 0;
            box-sizing: border-box;
            .avatar{
              width: 53px;
              height: 61px;
              border-radius: 2px;
              overflow: hidden;
              img{
                width: 100%;
              }
            }
            .name{
              color: #191B1F;
              font-size: 12px;
              margin: 8px 0 8px;
              font-weight: 500;
            }
            .concat{
              color: #909090;
              font-size: 8px;
            }
            .qrcode{
              width: 71px;
              height: 71px;
              overflow: hidden;
              img{
                width: 100%;
              }
            }
            .tips{
              margin-top: 14px;
              font-size: 11px;
              color: #191B1F;
            }
          }
        }
      }
      .friends{
        margin-top: 40px;
      }

    }
    .services{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px 11px;
      grid-auto-rows: min-content;
    }
    .about{
      margin-top: 30px;
      .items{
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 12px 11px;
        grid-auto-rows: min-content;
      }
    }
  }
}

// 移动端咨询弹窗样式
:deep(.mobile-consultation-pop) {
  .el-dialog {
    margin: 0;
    border-radius: 8px 8px 0 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100% !important;
    max-height: 80vh;
    overflow-y: auto;
  }

  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
    max-height: calc(80vh - 60px);
    overflow-y: auto;
  }

  .tip-box, .pay-box, .captcha-box, .btn-box {
    padding: 16px 20px;
    margin-bottom: 8px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .tip {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .captcha-container {
    display: flex;
    align-items: center;

    .captcha-image {
      width: 120px;
      height: 32px; // 与输入框高度保持一致
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409EFF;
      }
    }
  }

  .el-form-item {
    margin-bottom: 16px;
  }

  .el-button {
    border-radius: 6px;
  }
}
</style>