import { isMobile } from './device'

/**
 * 动态导入视图组件
 * @param viewName 视图名称，对应views目录下的文件夹名
 * @returns 返回对应设备的组件Promise
 */
export function dynamicViewImport(viewName: string) {
    const suffix = isMobile() ? 'mobile' : 'pc'
    return import(/* @vite-ignore */ `../views/${viewName}_${suffix}.vue`)
}
/**
 * 动态导入布局组件
 * @param layoutName 布局名称
 * @returns 返回对应设备的布局组件Promise
 */
export function dynamicLayoutImport(layoutName: string) {
    const suffix = isMobile() ? 'mobile' : 'pc'
    return import(/* @vite-ignore */ `../layouts/${layoutName}.${suffix}.vue`)
}
