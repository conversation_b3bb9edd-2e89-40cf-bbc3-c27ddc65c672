<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { expertList } from '@/api/common.ts'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import paginate from "@/components/paginate.vue";
import ExpertCard from "@/components/expertCard.vue";

const limit = ref(15)
const pageInfo = ref({
  title: '',
  subtitle: '',
})
const filters = ref({
  category_id: '',
  channel_id: '',
  keyword: '',
  research_field: ''
})
const page = ref(1)
const total = ref(0)
const list = ref([])
const getExpertList = () => {
  expertList({
    ...filters.value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    pageInfo.value = res.data.pageInfo
    total.value = res.data.pagination.total
    list.value = res.data.list
  })
}
const changePage = (e: number) => {
  page.value = e
  getExpertList()
}
const handleSearch = () => {
  page.value = 1
  getExpertList()
}
onMounted(() => {
  getExpertList()
})
</script>

<template>
<div class="main">
  <Header />
  <div class="banners">
    <div class="container">
      <img src="@/assets/images/expert/banner.png" alt="">
    </div>
  </div>
  <div class="filters">
    <div class="container bg-white">
      <div class="filter flex align-center justify-center">
        <div class="left">
          <span>关键字</span>
        </div>
        <div class="center flex align-center">
          <img class="cursor-pointer" src="@/assets/images/common/icon_search.png" alt="">
          <input type="text" class="flex-1" placeholder="搜索服务或关键词" v-model="filters.keyword" @keydown.enter="handleSearch">
        </div>
        <div class="right">
          <el-button color="#2D2B32" @click="handleSearch">搜索</el-button>
        </div>
      </div>
    </div>
  </div>
  <div class="items">
    <div class="container">
      <ExpertCard :info="item" is-flex :avatar-size="70" v-for="(item, index) in list" :key="index" />
    </div>
  </div>
  <div class="paginates">
    <div class="container">
      <paginate :limit="limit" :total="total" @change="changePage" />
    </div>
  </div>
  <Footer />
</div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .banners{
    margin: px2rem(20) 0;
    .container{
      img{
        width: 100%;
      }
    }
  }
  .filters{
    margin-bottom: px2rem(30);
    .container{
      padding: px2rem(26) 0;
      box-sizing: border-box;
      box-shadow: 0 px2rem(2) px2rem(12) 0 rgba(0,66,190,0.06);
      border-radius: px2rem(8);
      .filter{
        .left{
          color: #1F1F1F;
          font-size: px2rem(20);
          font-weight: 500;
        }
        .center{
          margin: 0 px2rem(10) 0 px2rem(20);
          border: px2rem(1) solid #2D2B32;
          border-radius: px2rem(8);
          width: px2rem(500);
          height: px2rem(50);
          line-height: px2rem(50);
          padding: px2rem(11) px2rem(22);
          box-sizing: border-box;
          img{
            width: px2rem(20);
            height: px2rem(20);
            margin-right: px2rem(8);
          }
          input{
            height: 100%;
            border: none;
            outline: none;
            font-size: px2rem(20);
            &::placeholder{
              color: #909090;
            }
          }
        }
        .right{
          .el-button{
            line-height: 1;
            padding: px2rem(13) px2rem(24);
            box-sizing: border-box;
            font-size: px2rem(18);
            height: auto;
            border-radius: px2rem(8);
          }
        }
      }
    }
  }
  .items{
    .container{
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: px2rem(27);

    }
  }
  .paginates{
    margin: px2rem(40) 0 px2rem(100);
  }
}
</style>