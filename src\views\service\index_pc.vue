<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { serviceList } from '@/api/common.ts'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import ServiceCard from "@/components/serviceCard.vue";
import paginate from "@/components/paginate.vue";
import RollingText from '@/components/RollingText.vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute();
const router = useRouter();
const limit = ref(12)
const pageInfo = ref({
  title: '',
  subtitle: '',
  totalCount: 0
})
const filters = ref({
  keyword: '',
  service_scenario: '',
  service_type: '',
})
const options = ref([
  {
    name: '服务场景',
    field: 'service_scenario',
    data: []
  },
  {
    name: '服务类型',
    field: 'service_type',
    data: []
  }
]);
const total = ref(0)
const page = ref(1)
const list = ref([])
const count = ref(1) // 改回初始值为1
const incrementStep = ref(1) // 添加响应式步长变量
const isCountInitialized = ref(false) // 添加标记，确保count只初始化一次

// 更新随机步长的函数
const updateRandomStep = () => {
  incrementStep.value = Math.floor(Math.random() * 20) + 1
}

// 初始化count的函数
const initCount = () => {
  if (!isCountInitialized.value) {
    count.value = 1
    isCountInitialized.value = true
  }
}

const getServiceList = () => {
  serviceList({
    ...filters.value,
    page: page.value,
    limit: limit.value
  }).then(res => {
    pageInfo.value = res.data.pageInfo
    total.value = res.data.pagination.total
    list.value = res.data.list
    options.value[0].data = res.data.options.scenarios
    options.value[1].data = res.data.options.types
  })
}
const changePage = (e: number) => {
  page.value = e
  getServiceList()
}
const handleSearch = () => {
  page.value = 1
  getServiceList()
}
const handleFilter = (item: any, item1: any) => {
  if (item.field === 'service_scenario') {
    filters.value.service_scenario = String(item1.key)
  } else if (item.field === 'service_type') {
    filters.value.service_type = String(item1.key)
    // 更新URL参数，保持状态同步
    const newQuery = { ...route.query }
    if (item1.key === '' || item1.key === null) {
      // 如果选择"全部类型"，删除category_id参数
      delete newQuery.category_id
    } else {
      // 更新category_id参数
      newQuery.category_id = String(item1.key)
    }
    router.replace({ query: newQuery })
  }
  page.value = 1
  getServiceList()
}

onMounted(() => {
  // 初始化count，确保只初始化一次
  initCount()
  
  if (route.query.category_id) {
    filters.value.service_type = String(route.query.category_id)
  }
  getServiceList()
  
  // 启动随机步长更新
  updateRandomStep() // 初始更新
  setInterval(updateRandomStep, 10000) // 每10秒更新一次随机步长，减少频率
})

// 监听路由参数变化
watch(() => route.query.category_id, (newCategoryId, oldCategoryId) => {
  if (newCategoryId !== oldCategoryId) {
    if (newCategoryId) {
      filters.value.service_type = String(newCategoryId)
    } else {
      filters.value.service_type = ''
    }
    page.value = 1
    getServiceList()
  }
}, { immediate: false })
</script>

<template>
  <div class="main">
    <Header/>
    <div class="banners">
      <div class="container flex flex-column align-center justify-center">
        <div class="title text-white">
          <span>{{pageInfo.title}}</span>
        </div>
        <div class="tips text-white" v-if="pageInfo.subtitle">
          <span>{{pageInfo.subtitle}}</span>
          <RollingText
              :key="'rolling-text-counter'"
              :value="count"
              :duration="600"
              :auto-increment="true"
              :increment-interval="3000"
              :increment-step="incrementStep"
          />
          <span>人次</span>
        </div>
        <div class="search bg-white flex align-center">
          <div class="icon hidden cursor-pointer flex" @click="handleSearch">
            <img src="../../assets/images/common/icon_search.png" alt="">
          </div>
          <div class="input flex-1">
            <input placeholder="搜索服务或关键词" v-model="filters.keyword" @keydown.enter="handleSearch">
          </div>
        </div>
      </div>
    </div>
    <div class="filters">
      <div class="container">
        <div class="filter flex justify-between" v-for="(item, index) in options" :key="index">
          <div class="name">
            <span>{{item.name}}</span>
          </div>
          <div class="value flex-1 flex align-center">
            <div class="item cursor-pointer" 
                   :class="{active: filters[item.field] == item1.key}"
                   @click="handleFilter(item, item1)" 
                   v-for="(item1, index1) in item.data" :key="index1">
              <span>{{(item1 as any).label}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="items">
      <div class="container">
        <ServiceCard :info="item" v-for="(item, index) in list" :key="index" />
      </div>
    </div>
    <div class="paginates">
      <div class="container">
        <paginate :limit="limit" :total="total" @change="changePage" />
      </div>
    </div>
    <Footer/>
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;

  .banners {
    width: 100%;
    height: px2rem(381);
    background-color: $maincolor;

    .container {
      height: 100%;
      .title {
        font-size: px2rem(50);
        font-weight: 600;
        letter-spacing: px2rem(3);
      }
      .tips{
        color: rgba(255,255,255,0.8);
        font-size: px2rem(24);
        font-weight: 400;
        margin: px2rem(14) 0 px2rem(30);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap; /* 防止换行 */
        overflow: visible; /* 确保内容不被截断 */
      }
      .search{
        width: px2rem(500);
        height: px2rem(54);
        line-height: px2rem(54);
        border-radius: px2rem(8);
        padding: 0 px2rem(22);
        box-sizing: border-box;
        .icon{
          width: px2rem(20);
          height: px2rem(20);
          margin-right: px2rem(8);
          img{
            width: 100%;
          }
        }
        .input{
          height: 100%;
          input{
            width: 100%;
            box-sizing: border-box;
            height: 100%;
            border: none;
            outline:  none;
            font-size: px2rem(20);
            &::placeholder{
              color: #909090;
            }
          }
        }
      }
    }
  }
  .filters{
    margin: px2rem(50) 0;
    .filter{
      &+.filter{
        margin-top: px2rem(36);
      }
      .name{
        color: #909090;
        font-weight: 400;
        font-size: px2rem(18);
        margin-right: px2rem(30);
      }
      .value{
        flex-wrap: wrap;
        :deep(.el-checkbox){
          margin-right: px2rem(50);
          height: auto;
          .el-checkbox__input{
            .el-checkbox__inner{
              border-radius: 50%;
            }
          }
          .el-checkbox__label{
            padding-left: px2rem(6);
            box-sizing: border-box;
            font-size: px2rem(18);
          }
        }
        .item{
          font-size: px2rem(18);
          font-weight: 400;
          margin-right: px2rem(24);
          &:last-child{
            margin-right: 0;
          }
          &:hover{
            color:  $maincolor;
          }
          &.active{
            color:  $maincolor;
          }
        }
      }
    }
  }
  .items{
    .container{
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: px2rem(20);

    }
  }
  .paginates{
    margin: px2rem(40) 0 px2rem(100);
  }
}
</style>