<template>
  <div class="rolling-text">
    <span
        v-for="(item, index) in displayChars"
        :key="`${index}-${item.current}`"
        class="rolling-text__character"
        :class="{ 'is-animating': item.isAnimating }"
        :style="{ '--duration': `${duration}ms` }"
    >
      <span class="rolling-text__digit">
        {{ item.current }}
      </span>
      <span 
        v-if="item.isAnimating" 
        class="rolling-text__digit rolling-text__digit--next"
      >
        {{ item.next }}
      </span>
    </span>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'

interface CharItem {
  current: string
  next: string
  isAnimating: boolean
}

const props = withDefaults(
    defineProps<{
      /** 要显示的值，可以是数字或字符串 */
      value: number | string
      /** 动画持续时间，单位毫秒，默认800ms */
      duration?: number
      /** 是否启用自动递增，默认false */
      autoIncrement?: boolean
      /** 自动递增的间隔时间，单位毫秒，默认2000ms */
      incrementInterval?: number
      /** 自动递增的步长，默认1 */
      incrementStep?: number
      /** 自动递增的最大值，null表示无限制，默认null */
      maxValue?: number | null
    }>(),
    {
      duration: 800,
      autoIncrement: false,
      incrementInterval: 2000,
      incrementStep: 1,
      maxValue: null
    }
)

const emit = defineEmits(['complete', 'update:value'])
const internalValue = ref(Number(props.value))
const displayChars = ref<CharItem[]>([])
let intervalId: NodeJS.Timeout | null = null

// 初始化显示字符
const initDisplayChars = () => {
  const str = String(internalValue.value)
  // 确保显示完整的数字，包括所有位数
  displayChars.value = str.split('').map(char => ({
    current: char,
    next: '',
    isAnimating: false
  }))
}

// 开始滚动动画
const startAnimation = async (newValue: number) => {
  // 安全检查：确保新值不小于当前值
  if (newValue < internalValue.value) {
    console.warn('RollingText: 新值小于当前值，跳过更新', { current: internalValue.value, new: newValue })
    return
  }

  // 如果是自动递增模式，不要使用外部传入的值
  if (props.autoIncrement) {
    return
  }

  const oldStr = String(internalValue.value)
  const newStr = String(newValue)

  // 补全位数使长度一致
  const maxLength = Math.max(oldStr.length, newStr.length)
  const paddedOld = oldStr.padStart(maxLength, ' ')
  const paddedNew = newStr.padStart(maxLength, ' ')

  // 为每个变化的数字设置动画
  for (let i = 0; i < maxLength; i++) {
    if (paddedOld[i] !== paddedNew[i] && !isNaN(Number(paddedNew[i]))) {
      const charIndex = i - (maxLength - displayChars.value.length)
      if (charIndex >= 0 && charIndex < displayChars.value.length) {
        // 设置下一个数字
        displayChars.value[charIndex].next = paddedNew[i]
        
        // 等待DOM更新后再开始动画
        await nextTick()
        
        // 使用requestAnimationFrame确保在下一帧开始动画
        requestAnimationFrame(() => {
          // 开始动画
          displayChars.value[charIndex].isAnimating = true
          
          // 动画结束后更新当前值
          setTimeout(() => {
            displayChars.value[charIndex].current = paddedNew[i]
            displayChars.value[charIndex].isAnimating = false
            displayChars.value[charIndex].next = ''
          }, props.duration)
        })
      }
    }
  }

  internalValue.value = newValue
  emit('update:value', newValue)

  setTimeout(() => {
    emit('complete')
  }, props.duration)
}

// 自动递增
const startAutoIncrement = () => {
  if (intervalId) clearInterval(intervalId)

  intervalId = setInterval(() => {
    const newValue = internalValue.value + props.incrementStep
    
    // 打印调试信息
    console.log('RollingText 调试信息:', {
      currentValue: internalValue.value,
      incrementStep: props.incrementStep,
      newValue: newValue,
      displayChars: displayChars.value.map(char => char.current).join(''),
      timestamp: new Date().toLocaleTimeString()
    })
    
    // 安全检查：确保新值不小于当前值
    if (newValue < internalValue.value) {
      console.warn('RollingText: 新值小于当前值，跳过更新', { current: internalValue.value, new: newValue })
      return
    }
    
    // 使用动画更新数字
    const oldStr = String(internalValue.value)
    const newStr = String(newValue)
    
    // 补全位数使长度一致
    const maxLength = Math.max(oldStr.length, newStr.length)
    const paddedOld = oldStr.padStart(maxLength, ' ')
    const paddedNew = newStr.padStart(maxLength, ' ')
    
    // 如果位数增加了，需要扩展displayChars数组
    if (newStr.length > oldStr.length) {
      const newDisplayChars = newStr.split('').map(char => ({
        current: char,
        next: '',
        isAnimating: false
      }))
      displayChars.value = newDisplayChars
    }
    
    // 打印动画调试信息
    console.log('RollingText 动画调试:', {
      oldStr,
      newStr,
      paddedOld,
      paddedNew,
      maxLength,
      displayCharsLength: displayChars.value.length
    })
    
    // 为每个变化的数字设置动画
    for (let i = 0; i < maxLength; i++) {
      if (paddedOld[i] !== paddedNew[i] && !isNaN(Number(paddedNew[i]))) {
        const charIndex = i - (maxLength - displayChars.value.length)
        if (charIndex >= 0 && charIndex < displayChars.value.length) {
          // 设置下一个数字
          displayChars.value[charIndex].next = paddedNew[i]
          displayChars.value[charIndex].isAnimating = true
          
          // 动画结束后更新当前值
          setTimeout(() => {
            displayChars.value[charIndex].current = paddedNew[i]
            displayChars.value[charIndex].isAnimating = false
            displayChars.value[charIndex].next = ''
            
            // 打印动画完成后的状态
            console.log('RollingText 动画完成:', {
              finalValue: internalValue.value,
              displayChars: displayChars.value.map(char => char.current).join(''),
              timestamp: new Date().toLocaleTimeString()
            })
          }, props.duration)
        }
      }
    }
    
    // 更新内部值
    internalValue.value = newValue
    emit('update:value', newValue)
  }, props.incrementInterval)
}

// 监听props变化
watch(
    () => props.value,
    (newVal) => {
      const numVal = Number(newVal)
      if (!isNaN(numVal) && numVal !== internalValue.value) {
        // 只有在非自动递增模式下才响应外部值变化
        if (!props.autoIncrement) {
          startAnimation(numVal)
        }
      }
    },
    { immediate: true }
)

watch(
    () => props.autoIncrement,
    (newVal) => {
      if (newVal) {
        startAutoIncrement()
      } else if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
    },
    { immediate: true }
)

// 监听incrementStep变化，重新启动自动递增
watch(
    () => props.incrementStep,
    () => {
      if (props.autoIncrement && intervalId) {
        // 不要重新启动，只更新步长，保持当前数字不变
        // startAutoIncrement()
      }
    }
)

onMounted(() => {
  initDisplayChars()
  if (props.autoIncrement) {
    startAutoIncrement()
  }
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<style scoped>
.rolling-text {
  display: inline-flex;
  overflow: visible; /* 改为visible，确保所有数字都能显示 */
  font-size: inherit;
  line-height: 1;
  font-family: monospace;
  white-space: nowrap; /* 确保数字不换行 */
  min-width: fit-content; /* 确保容器宽度适应内容 */
  flex-shrink: 0; /* 防止被压缩 */
}

.rolling-text__character {
  position: relative;
  display: inline-block;
  height: 1em;
  overflow: hidden;
  min-width: 0.6em; /* 确保每个字符有最小宽度 */
  flex-shrink: 0; /* 防止字符被压缩 */
}

.rolling-text__digit {
  display: block;
  height: 1em;
  transition: transform var(--duration) cubic-bezier(0.645, 0.045, 0.355, 1);
}

.rolling-text__digit--next {
  position: absolute;
  top: 100%;
  left: 0;
  transform: translateY(0);
  transition: transform var(--duration) cubic-bezier(0.645, 0.045, 0.355, 1);
  /* 确保新数字初始位置在下方且不可见 */
  opacity: 0;
  transition: transform var(--duration) cubic-bezier(0.645, 0.045, 0.355, 1), opacity var(--duration) cubic-bezier(0.645, 0.045, 0.355, 1);
}

.rolling-text__character.is-animating .rolling-text__digit {
  transform: translateY(-100%);
}

.rolling-text__character.is-animating .rolling-text__digit--next {
  transform: translateY(-100%);
  opacity: 1;
}
</style> 