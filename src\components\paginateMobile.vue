<template>
  <div class="paginate flex justify-center align-center">
    <el-pagination :default-page-size="limit" hide-on-single-page background layout="prev, pager, next, total" size="small" :total="total" @change="change" />
  </div>
</template>

<script setup lang="ts">
const { total } = defineProps({
  total: {
    type: Number,
    required: true,
  },
  limit: {
    type: Number,
    required: false,
    default: 10,
  }
})

const emits = defineEmits(["change"]);

const change = (e: number) => {
  emits('change', e);
}
</script>

<style scoped lang="scss">
.paginate{
  .total{
    font-size: 14px;
  }
  :deep(.el-pagination){
    .el-pager{
      .number{
        font-size: 14px;
        background-color: $whitecolor;
        color: #59617F;
        width: 17px;
        height: 17px;
        padding: 0;
        box-sizing: border-box;
        &.is-active{
          color: #fff;
          background-color: $maincolor;
        }
      }
    }
    .btn-prev{
      background-color: $whitecolor;
      color: #59617F;
      width: 17px;
      height: 17px;
      padding: 0;
      box-sizing: border-box;
    }
    .btn-next{
      background-color: $whitecolor;
      color: #59617F;
      width: 17px;
      height: 17px;
      padding: 0;
      box-sizing: border-box;
    }
    .el-pagination__total{
      color: #8E94A1;
      font-size: 14px;
    }
  }

}
</style>
