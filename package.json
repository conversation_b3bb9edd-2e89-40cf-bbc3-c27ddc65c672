{"name": "academic", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@types/node": "^22.15.30", "axios": "^1.9.0", "element-plus": "^2.10.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "qrcode": "^1.5.4", "sass": "^1.89.1", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "vconsole": "^3.15.1", "vite-plugin-inspect": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue3-scroll-seamless": "^1.0.6"}, "devDependencies": {"@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}