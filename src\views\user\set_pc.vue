<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import userInfoCard from "@/components/userInfoCard.vue";
import userSliderCard from "@/components/userSliderCard.vue";
import { useUserInfoStore } from '@/store/userInfo.ts'
import type {FormInstance} from "element-plus";
const userInfoStore = useUserInfoStore();
const formRef = ref()
const phoneRef = ref()
const form = ref({
  phone: '',
  wechat: '',
})
const formPhone = ref({
  phone: '',
  code: ''
})
const showPop = ref(false)
const rules = ref({
  phone: [
    {  required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ],
  code: [
      {  required: true, message: '请输入验证码', trigger: 'blur' },
  ]
})
const openPop = () => {
  showPop.value = true
}
const getCode = () => {

}
const confirm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
    }
  })
}
onMounted(() => {
  form.value.phone = JSON.parse(JSON.stringify(userInfoStore.userInfo))?.mobile
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="userinfo">
      <div class="container">
        <userInfoCard />
      </div>
    </div>
    <div class="boxs flex justify-between">
      <div class="container flex">
        <div class="sliders">
          <userSliderCard />
        </div>
        <div class="box bg-white flex-1">
          <div class="title">
            <span>账号设置</span>
          </div>
          <div class="form">
            <el-form ref="formRef" :model="form" label-width="100px" label-position="left">
              <el-form-item label="手机号" prop="phone">
                <el-input type="number" maxlength="11" v-model="form.phone" disabled placeholder="请输入手机号">
                  <template #append>
                    <el-button @click="openPop">修改</el-button>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="微信号" prop="wechat">
                <el-input type="number" v-model="form.wechat" placeholder="请输入微信号" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
  </div>
  <el-dialog title="修改手机号" center v-model="showPop" width="500px" :close-on-click-modal="false" align-center class="changeMobilePop">
    <div class="popup">
      <el-form ref="phoneRef" :model="formPhone" :rules="rules">
        <el-form-item label="" prop="phone">
          <el-input maxlength="11" v-model="formPhone.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="" prop="code">
          <el-input maxlength="6" v-model="formPhone.code" placeholder="请输入验证码">
            <template #append>
              <el-button :disabled="formPhone.phone != '' || formPhone.code != ''" @click="getCode">获取验证码</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <div class="confirm flex-1">
            <el-button type="primary" @click="confirm(phoneRef)">修改</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .userinfo{
    margin: px2rem(20) 0;
  }
  .boxs{
    .sliders{
      width: px2rem(238);
    }
    .box{
      margin-left: px2rem(20);
      border-radius: px2rem(16);
      padding: px2rem(24) 0 px2rem(40);
      box-sizing: border-box;
      .title{
        color: #1F1F1F;
        font-size: px2rem(18);
        font-weight: 500;
        border-bottom: px2rem(1) solid #E9E9E9;
        padding: px2rem(0) px2rem(40) px2rem(16);
        box-sizing: border-box;
      }
      .form{
        padding: px2rem(30) px2rem(40);
        box-sizing: border-box;
        :deep(.el-form){
          .el-form-item{
            .el-form-item__content{
              .el-input{
                .el-input__wrapper{
                  background-color: #F7FAFE;
                  box-shadow: unset;
                }
                .el-input-group__append{
                  box-shadow: unset;
                  background-color: #F7FAFE;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
  }
  .empty{
    height: px2rem(100);
  }
}
.popup{
  :deep(.el-form){
    .el-form-item{
      margin-bottom: px2rem(32);
      &:last-child{
        margin-bottom: 0;
      }
      .el-form-item__content{
        .el-input{
          .el-input__wrapper{

            .el-input__inner{
              height: px2rem(48);
            }
          }
          .el-input-group__append{
            background-color: transparent;
            color: $maincolor;
            .el-button{
              font-weight: unset;
            }
          }
        }
        .confirm{
          .el-button{
            width: 100%;
            height: px2rem(48);
            border-radius: 8px;
            font-size: px2rem(16);
          }
        }
      }
    }
  }
}

</style>