import { defineStore } from 'pinia';

export const useSystemStore = defineStore('system', {
  state: () => ({
    menus: [],
    openMemberCenter: false,
    rules: [],
    site: {
      banner: [],
      cdnUrl: '',
      firind_link: [],
      recommended: {
        experts: [],
        journals: [],
        services: []
      },
      category: [],
      home: {
        tab1_title: '',
        tab1_desc: '',
        tab2_title: '',
        tab2_desc: '',
        tab3_title: '',
        tab3_desc: '',
        tab4_title: '',
        tab4_desc: '',
        tab5_title: '',
        tab5_desc: '',
      },
      customer_result: [],
      HEADLINE: {
        featured: [],
        list: []
      },
      siteName: ''
    },
    userInfo: [],
    isLogin: false,
    wx_qrcode: '',
    kefu_qrcode: '',
    kefu_url: '',
    yhxy: '',
    yszc: '',
    bqsm: '',
    base_emal: '',
    base_jobtime: '',
    base_tel: '',
    hiddenNav: [],
    qk_image: []
  }),

  getters: {
    getMenus(state) {
      return state.menus;
    },
  },

  actions: {
    setMenus(data: any) {
      this.menus = data;
    },
    setOpenMemberCenter(data: any) {
      this.openMemberCenter = data;
    },
    setRules(data: any) {
      this.rules = data;
    },
    setSite(data: any) {
      this.site = data;
    },
    setUserInfo(data: any) {
      this.userInfo = data;
    },
    setIsLogin(data: any) {
      this.isLogin = data;
    },
    setWxQrcode(data: any){
      this.wx_qrcode = data;
    },
    setKefuQrcode(data: any){
      this.kefu_qrcode = data;
    },
    setYhxy(data: any){
      this.yhxy = data;
    },
    setBqsm(data: any){
      this.bqsm = data;
    },
    setYszc(data: any){
      this.yszc = data;
    },
    setKefuUrl(data: any){
      this.kefu_url = data;
    },
    setBaseEmal(data: any){
      this.base_emal = data;
    },
    setBaseJobtime(data: any){
      this.base_jobtime = data;
    },
    setBaseTel(data: any){
      this.base_tel = data;
    },
    setHiddenNav(data: any) {
      this.hiddenNav = data;
    },
    setQkImage(data: any){
      this.qk_image = data;
    }
  },

  persist: true,
});
