<script setup lang="ts">
import {ref, onUnmounted, computed} from 'vue'
import {Close} from "@element-plus/icons-vue";
import { oauthList } from '@/api/oauth.ts'
import { useSystemStore } from '@/store/system'
import {getAssetsFile} from "@/utils/utils.ts";
import {ElMessage, type FormInstance, type FormRules} from "element-plus";
import {checkIn, sendSms} from "@/api/login.ts";
import {safePush} from "@/utils/router.ts";
import { useUserInfoStore } from '@/store/userInfo.ts'
import {onLogin} from "@/utils/oauth.ts";
import HeaderTitle from "@/components/headerTitle.vue";
const userInfoStore = useUserInfoStore()
const systemStore = useSystemStore()
const popupKey = ref('')
const isAgree = ref(true)
const visible = ref(false)
const dialogVisible = ref(false)
const availableOauthNames = ref<string[]>([])
const loginTypes = ref([
  {id: 0, name: '微信登录', code: 'wechat', title: '微信扫码注册登录', icon: getAssetsFile('common/icon_wx.png'), iconHover: getAssetsFile('common/icon_wx_active.png'), oauthName: 'wechat_scan'},
  {id: 1, name: '手机号登录', code: 'mobile', title: '手机号注册登录', icon: getAssetsFile('common/icon_mobile.png'), iconHover: getAssetsFile('common/icon_mobile_active.png'), oauthName: ''},
  {id: 2, name: 'QQ登录', code: 'qq', title: 'QQ扫码注册登录', icon: getAssetsFile('common/icon_qq.png'), iconHover: getAssetsFile('common/icon_qq_active.png'), oauthName: 'qq'},
])
const loginType = ref(0)
const formData = ref({
  mobile: '',
  captcha: ''
})
const formRef = ref()
const countdown = ref(0)
const timer = ref<number | null>(null)
const rules = ref<FormRules>({
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号错误', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码错误', trigger: 'blur' }
  ]
})

const loginOtherTypes = computed(() => {
  return loginTypes.value.filter((_, index) => index !== loginType.value)
})

// 获取可用的OAuth平台列表
const getOauthList = async () => {
  try {
    const response: any = await oauthList()
    if (response && response.code === 1) {
      availableOauthNames.value = response.data.oauthNames || []
    }
  } catch (error) {
    console.error('获取OAuth平台列表失败:', error)
    // 如果失败，设置默认的OAuth平台（基于后端系统的配置）
    availableOauthNames.value = ['qq', 'wechat_scan', 'wechat_mp', 'wechat_mini_program']
  }
}
const handleChangeLoginType = (e: any) => {
  // 检查是否是OAuth登录
  if (e.oauthName && availableOauthNames.value.includes(e.oauthName)) {
    if (!isAgree.value) {
      return ElMessage.error('请阅读并同意《用户协议》、《版权声明》、《隐私政策》')
    }
    // 微信登录需要特殊处理
    if (e.oauthName === 'wechat_scan') {

    } else {
      // 其他OAuth登录直接跳转
      console.log('其他OAuth登录:', e.oauthName)
      onLogin(e.oauthName)
    }
  } else if (e.code === 'mobile') {
    // 手机号登录
    console.log('切换到手机号登录')
    loginType.value = e.id
  } else {
    // 其他登录方式暂时不可用
    console.log('登录方式不可用:', e)
    ElMessage.warning(`${e.name}暂不可用`)
  }
}
const handleMaskClick = (e: MouseEvent) => {
  // 确保点击的是遮罩层本身，而不是其子元素
  if (e.target === e.currentTarget) {
    hide()
  }
}
// 检查登录类型是否可用
const isLoginTypeAvailable = (loginTypeItem: any) => {
  if (loginTypeItem.oauthName) {
    return availableOauthNames.value.includes(loginTypeItem.oauthName)
  }
  return true // 非OAuth登录类型默认可用
}
const handleGetCode = () => {
  if (countdown.value > 0) return
  if (!formData.value.mobile) {
    ElMessage.warning('请输入手机号')
    return
  }
  sendSms({
    template_code: 'user_login',
    mobile: formData.value.mobile,
  }).then((res: any) => {
    console.log(res)
    countdown.value = 60
    timer.value = window.setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        timer.value && clearInterval(timer.value)
      }
    }, 1000)
    ElMessage.success(res.msg)
  })
}
const handleLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  try {
    const valid = await formEl.validate()
    if (valid) {
      if (!isAgree.value) {
        ElMessage.error('请阅读并同意《用户协议》、《版权声明》、《隐私政策》')
        return
      }
      const res = await checkIn({
        tab: 'mobile_login',
        keep: 1,
        mobile: formData.value.mobile,
        captcha: formData.value.captcha
      })
      ElMessage.success(res.msg)
      userInfoStore.setUserInfo(res.data.userInfo)
      userInfoStore.setIsLogin(true)
      hide()
      setTimeout(() => {
        safePush('/user')
      }, 100);
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 监听父组件传递的显示状态
const show = async () => {
  visible.value = true
  // 禁止背景滚动
  document.body.style.overflow = 'hidden'
  if (availableOauthNames.value.length === 0) {
    await getOauthList()
  }
}
const hide = () => {
  visible.value = false
  // 恢复背景滚动
  document.body.style.overflow = ''
}

const showPop = (e: string) => {
  popupKey.value = e
  dialogVisible.value = true
}
// 组件卸载时确保恢复滚动和清除定时器
onUnmounted(() => {
  document.body.style.overflow = ''
})

defineExpose({
  show,
  hide
})
</script>

<template>
  <Teleport to="body">
    <div v-show="visible" class="login-mask position-fixed" @click="handleMaskClick">
      <div class="loginpopup position-absolute flex flex-column justify-between bg-white">
        <HeaderTitle @click="hide" />
        <div class="body flex-1">
          <div class="logo flex flex-column align-center justify-center">
            <img src="@/assets/images/common_mobile/login_logo.png" alt="">
            <img src="@/assets/images/common_mobile/logo_name.png" alt="">
          </div>
          <div class="label">
            <span>{{['登录/注册', '手机号登录'][loginType]}}</span>
          </div>
          <template v-if="loginType === 0">
            <div class="buttons">
              <el-button color="#75CC61">
                <img src="@/assets/images/common_mobile/icon_wx1.png" alt="">
                <span class="text-white">微信登录</span>
              </el-button>
            </div>
          </template>
          <template v-else-if="loginType === 1">
            <div class="phonebox">
              <el-form ref="formRef" :model="formData" :rules="rules">
                <el-form-item prop="mobile">
                  <el-input v-model="formData.mobile" placeholder="请输入手机号" maxlength="11"></el-input>
                </el-form-item>
                <el-form-item prop="captcha">
                  <el-input v-model="formData.captcha" placeholder="请输入验证码" maxlength="6">
                    <template #suffix>
                      <div @click.stop.prevent>
                        <el-link type="primary" underline="never" :class="{codeing: countdown}" @click.stop.prevent="handleGetCode">
                          {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
                        </el-link>
                      </div>
                    </template>
                  </el-input>
                </el-form-item>
              </el-form>
              <div class="confirm">
                <el-button class="text-white" @click="handleLogin(formRef)">登录</el-button>
              </div>
            </div>
          </template>
          <div class="logintypes flex align-center justify-between" :class="loginTypes[loginType].code">
            <div class="logintype flex align-center cursor-pointer"
                 v-for="(item, index) in loginOtherTypes"
                 :key="index"
                 @click="handleChangeLoginType(item)"
                 :class="{ 'disabled': !isLoginTypeAvailable(item) }">
              <el-image :src="item.icon"></el-image>
              <span>{{item.name}}</span>
            </div>
          </div>
          <div class="tips position-absolute flex align-center justify-center">
            <el-checkbox v-model="isAgree">同意并阅读</el-checkbox>
            <el-link underline="never" @click="showPop('userAgreement')">《用户协议》</el-link>
            <span>、</span>
            <el-link underline="never" @click="showPop('copyrightNotice')">《版权声明》</el-link>
            <span>、</span>
            <el-link underline="never" @click="showPop('privacyPolicy')">《隐私政策》</el-link>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
  <el-dialog
      v-model="dialogVisible"
      :title="popupKey === 'userAgreement' ? '用户协议' : popupKey === 'copyrightNotice' ? '版权声明' : popupKey === 'privacyPolicy' ? '隐私政策' : '' "
      width="90%"
      class="loginTexts"
      :show-close="false"
  >
    <div class="content">
      <template v-if="popupKey === 'userAgreement'">
        <div v-html="systemStore.yhxy"></div>
      </template>
      <template v-else-if="popupKey === 'copyrightNotice'">
        <div v-html="systemStore.bqsm"></div>
      </template>
      <template v-else-if="popupKey === 'privacyPolicy'">
        <div v-html="systemStore.yszc"></div>
      </template>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
  .login-mask {
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    .loginpopup {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1000;

      .body{
        background: url("@/assets/images/common_mobile/bg_login.png") no-repeat;
        background-size: cover;
        padding: 40px;
        box-sizing: border-box;
        .logo{
          img{
            &:nth-child(1){
              width: 80px;
            }
            &:nth-child(2){
              margin-top: 16px;
              width: 90px;
            }
          }
        }
        .label{
          font-size: 14px;
          font-weight: 500;
          margin: 26px 0 12px;
        }
        .buttons{
          img{
            width: 22px;
            margin-right: 7px;
          }
          .el-button{
            width: 100%;
            font-size: 14px;
            font-weight: 500;
            height: 48px;
            line-height: 48px;
            border-radius: 8px;
          }
        }
        .phonebox{
          margin-top: 12px;
          :deep(.el-form){
            width: 100%;
            .el-form-item{
              &+.el-form-item{
                margin-top: 26px;
              }
              .el-form-item__content{
                .el-input{
                  .el-input__wrapper{
                    border-radius: 8px;
                    box-shadow: unset;
                    &.is-focus{
                      box-shadow: unset;
                    }
                    .el-input__inner{
                      height: 48px;
                      line-height: 48px;
                      font-size: 16px;
                    }
                    .el-input__suffix{
                      .el-input__suffix-inner{
                        .el-link{
                          color: $maincolor;
                          font-size: 16px;
                          &.codeing{
                            color: #8E94A1;
                          }
                          .el-link__inner{
                            &:hover{
                              color: unset;
                            }
                          }
                        }
                      }
                    }
                  }
                }
                .el-form-item__error{
                  padding: 6px 0 0 20px;
                  font-size: 12px;
                  color: #E5331B;
                }
              }
            }
          }
          .confirm{
            margin-top: 30px;
            .el-button{
              width: 100%;
              height: 48px;
              background: linear-gradient( 180deg, #39B3E6 0%, $maincolor 100%);
              border-radius: 8px;
              font-size: 16px;
              font-weight: 500;
              &:hover {
                color: $whitecolor;
              }
            }
          }
        }
        .logintypes{
          margin-top: 30px;
          &.wechat{

          }
          .logintype{
            font-size: 14px;
            color: #535871;
            .el-image{
              width: 36px;
              height: 36px;
              border-radius: 50%;
              margin-right: 10px;
            }
            &.disabled {
              opacity: 0.5;
              cursor: not-allowed;
              pointer-events: none;
            }
          }
        }
        .tips{
          width: 100%;
          background: #FAFAFC;
          bottom: 35px;
          left: 0;
          :deep(.el-checkbox){
            .el-checkbox__input{
              &.is-checked{
                .el-checkbox__inner{
                  background-color: #69CDBA;
                  border-color: #69CDBA;
                }
              }
              .el-checkbox__inner{
                width: 14px;
                height: 14px;
              }
            }
            .el-checkbox__label{
              color: #7280A7;
              font-size: 12px;
            }
          }
          :deep(.el-link){
            color: #191B1F;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>