<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { serviceList } from '@/api/common.ts'
import HeaderMobile from "@/components/headerMobile.vue";
import ServiceCardMobile from "@/components/serviceCardMobile.vue";
import paginate from "@/components/paginateMobile.vue";
import { useRoute, useRouter } from 'vue-router'
import { Close } from '@element-plus/icons-vue'
import { useSystemStore } from '@/store/system'
import HeaderTitle from "@/components/headerTitle.vue";
const systemStore = useSystemStore()
const route = useRoute();
const router = useRouter();
const containerRef = ref<HTMLElement | null>(null)
const show = ref(false)
const limit = ref(10)
const pageInfo = ref({
  title: '',
  subtitle: '',
  totalCount: 0
})
const filters = ref({
  keyword: '',
  service_scenario: '',
  service_type: '',
})
const tempFilters = ref({
  service_scenario: '',
  service_type: '',
})
const optionCur = ref(0)
const options = ref([
  {
    name: '服务场景',
    field: 'service_scenario',
    data: []
  },
  {
    name: '服务类型',
    field: 'service_type',
    data: []
  }
]);
const total = ref(0)
const page = ref(1)
const list = ref([])

const getServiceList = () => {
  serviceList({
    ...filters.value,
    page: page.value,
    limit: limit.value,
  }).then(res => {
    pageInfo.value = res.data.pageInfo
    total.value = res.data.pagination.total
    list.value = res.data.list
    options.value[0].data = res.data.options.scenarios
    options.value[1].data = res.data.options.types
  })
}
const changePage = (e: number) => {
  page.value = e
  getServiceList()
}

const handleShowFilter = () => {
  for(let i in tempFilters.value){
    tempFilters.value[i] = filters.value[i]
  }
  show.value = true
}
const handleFilterConfirm = () => {
  for(let i in tempFilters.value){
    filters.value[i] = tempFilters.value[i]
  }
  show.value = false
  page.value = 1
  getServiceList()
}
const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
onMounted(() => {
  if (route.query.category_id) {
    filters.value.service_type = String(route.query.category_id)
  }
  getServiceList()
})

// 监听路由参数变化
watch(() => route.query.category_id, (newCategoryId, oldCategoryId) => {
  if (newCategoryId !== oldCategoryId) {
    if (newCategoryId) {
      filters.value.service_type = String(newCategoryId)
    } else {
      filters.value.service_type = ''
    }
    page.value = 1
    getServiceList()
  }
}, { immediate: false })
</script>

<template>
  <div class="page flex flex-column">
    <HeaderMobile />
    <div class="filters flex align-center justify-between">
      <div class="values flex align-center" v-if="filters.service_scenario || filters.service_type">
        <div class="value" v-for="(item, index) in options" :key="index">
          <span v-if="filters[item.field]">{{item.data.find(child=>child.key===filters[item.field])?.label}}</span>
        </div>
      </div>
      <div class="name" v-else>筛选</div>
      <div class="icon" @click="handleShowFilter">
        <img class="flex" src="@/assets/images/common_mobile/icon_filter.png" alt="">
      </div>
    </div>
    <div class="items auto flex-1">
      <div class="list">
        <ServiceCardMobile :info="item" v-for="(item, index) in list" :key="index" />
      </div>
    </div>
    <div class="paginates" v-if="total > limit">
      <paginate :limit="limit" :total="total" @change="changePage" />
    </div>
  </div>
  <el-drawer v-model="show" :show-close="false" size="100%" class="service-filters-popup">
    <template #header="{ close }">
      <HeaderTitle @click="close" />
    </template>
    <el-anchor :container="containerRef" class="filter flex justify-between flex-1 hidden" @click="handleClick">
      <div class="slide auto">
        <el-anchor-link class="option text-center" :class="{active: optionCur==index}" v-for="(item, index) in options" :key="index" :href="`#${item.name}`" @click="optionCur = index">
          <span>{{item.name}}</span>
        </el-anchor-link>
      </div>
      <div ref="containerRef" class="box bg-white flex-1 auto">
        <div class="item" v-for="(item, index) in options" :key="index">
          <div class="title">
            <span>{{item.name}}</span>
          </div>
          <div class="children">
            <div class="child text-center" :class="{active: tempFilters[item.field] == item1.key}" v-for="(item1, index1) in item.data" :key="index1" @click="tempFilters[item.field] = item1.key">
              <span>{{item1.label}}</span>
            </div>
          </div>
        </div>
      </div>
    </el-anchor>
    <template #footer>
      <div class="buttons flex align-center justify-between">
        <div class="button cancel flex-1 text-center" @click="show = false">取消</div>
        <div class="button confirm flex-1 text-center" @click="handleFilterConfirm">确认</div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped lang="scss">
.page {
  background: #FDFDFF;
  height: 100vh;
  .filters{
    margin: 18px 0 14px 15px;
    .name{
      font-size: 12px;
      color: #535871;
    }
    .values{
      .value{
        font-size: 12px;
        &+.value{
          margin-left: 12px;
        }
      }
    }
    .icon{
      width: 51px;
      height: 17px;
      img{
        width: 100%;
      }
    }
  }
  .items{
    padding: 0 15px;
    box-sizing: border-box;
    .list{
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 12px;
      grid-auto-rows: min-content;
    }
  }
  .paginates{
    margin: 10px 0;
  }
}
</style>