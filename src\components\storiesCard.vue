<script setup lang="ts">
import {safePush} from "@/utils/router.ts";
const { info } = defineProps({
  info: {
    type: Object,
    required: true,
  }
});
</script>

<template>
  <div class="item cursor-pointer bg-white" @click="safePush({path: '/stories/details', query:{id: info.id}})">
    <div class="title row1">
      <span v-html="info.title"></span>
    </div>
    <div class="tags flex align-center">
      <div class="tag">
        <span>{{info.author}}</span>
      </div>
      <div class="time">
        <span>{{info.publish_date}}</span>
      </div>
    </div>
    <div class="desc row3">
      <span>{{info.summary}}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item{
  border: px2rem(1) solid #E6EBF0;
  border-radius: px2rem(24);
  padding: px2rem(20);
  box-sizing: border-box;
  .title{
    color: #191B1F;
    font-size: px2rem(18);
    line-height: 1;
    font-weight: bold;
  }
  .tags{
    margin: px2rem(8) 0 px2rem(12);
    .tag{
      color: #3670EE;
      background-color: #EBF1FD;
      font-size: px2rem(14);
      border-radius: px2rem(4);
      line-height: 1;
      padding: px2rem(5) px2rem(10);
      box-sizing: border-box;
      display: inline-block;
    }
    .time{
      color: #191B1F;
      font-size: px2rem(14);
      margin: 0 px2rem(14);
    }
  }
  .desc{
    font-size: px2rem(14);
    color: #191B1F;
  }
}
</style>