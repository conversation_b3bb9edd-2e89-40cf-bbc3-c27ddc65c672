<script setup lang="ts">
import {safePush} from "@/utils/router.ts";

const props = defineProps({
  title: {
    type: String,
    required: true,
    default: '',
  },
  icon: {
    type: String,
    required: false,
    default: '',
  },
  moreUrl: {
    type: String,
    required: false,
    default: '',
  },
  padding: {
    type: String,
    required: false,
    default: '30px 15px',
  }
})
</script>

<template>
  <div class="others bg-white" :style="{padding: props.padding}">
    <div class="name-bar flex align-center justify-between">
      <div class="left flex align-center">
        <img :src="props.icon" alt="">
        <span>{{props.title}}</span>
      </div>
      <div class="right cursor-pointer flex align-center" @click="safePush(props.moreUrl)" v-if="moreUrl">
        <span>查看更多</span>
        <img src="@/assets/images/common/icon_right.png" alt="">
      </div>
    </div>
    <slot />
  </div>
</template>

<style scoped lang="scss">
.others{
  box-sizing: border-box;
  .name-bar{
    margin-bottom: 15px;
    .left{
      color: #191B1F;
      font-size: 16px;
      font-weight: bold;
      img{
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
    .right{
      color: #8A8A8A;
      font-size: 14px;
      img{
        width: 16px;
        height: 16px;
      }
    }
  }
}
</style>