import Axios from './axios';

export const sendSms = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/sms/send`,
        data: data,
    })
};

export const checkIn = (data: any): Promise<any> => {
    return Axios({
        method: 'post',
        url: `/api/user/checkIn`,
        data: data,
    })
};

export const loginThird = (data: any): Promise<any> => {
    return Axios({
        method: 'get',
        url: `/api/OAuthLogin/loginAgent`,
        params: data,
    })
};

