<script setup lang="ts">
import {safePush} from "@/utils/router.ts";

const props = defineProps({
  title: {
    type: String,
    required: true,
    default: '',
  },
  icon: {
    type: String,
    required: false,
    default: '',
  },
  moreUrl: {
    type: String,
    required: false,
    default: '',
  }
})
</script>

<template>
  <div class="others bg-white">
    <div class="name-bar flex align-center justify-between">
      <div class="left flex align-center">
        <img :src="props.icon" alt="">
        <span>{{props.title}}</span>
      </div>
      <div class="right cursor-pointer flex align-center" @click="safePush(props.moreUrl)" v-if="moreUrl">
        <span>查看更多</span>
        <img src="@/assets/images/common/icon_right.png" alt="">
      </div>
    </div>
    <slot />
  </div>
</template>

<style scoped lang="scss">
.others{
  padding: px2rem(24) px2rem(13);
  box-sizing: border-box;
  box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
  border-radius: px2rem(16);
  .name-bar{
    margin-bottom: px2rem(22);
    .left{
      color: #191B1F;
      font-size: px2rem(20);
      font-weight: bold;
      img{
        width: px2rem(24);
        height: px2rem(24);
        margin-right: px2rem(10);
      }
    }
    .right{
      color: #8A8A8A;
      font-size: px2rem(14);
      img{
        width: px2rem(16);
        height: px2rem(16);
      }
    }
  }
}
</style>