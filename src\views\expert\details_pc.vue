<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Star, StarFilled } from '@element-plus/icons-vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import JournalCard from "@/components/journalCard.vue";
import friendCard from "@/components/friendCard.vue";
import aboutCard from "@/components/aboutCard.vue";
import otherCard from "@/components/otherCard.vue";
import serviceCard from "@/components/serviceCard.vue";
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import {safePush} from "@/utils/router.ts";
import { contentDetail, toggleCollect } from '@/api/common.ts'
import { useRoute } from 'vue-router'
import ShareCard from "@/components/shareCard.vue";
import { useSystemStore } from '@/store/system.ts'
const systemStore = useSystemStore()
import { ElMessage } from 'element-plus'
const route = useRoute();

const info = ref({
  detail: {},
  related: {
    services: [],
    experts: [],
    journals: []
  },
  recommended: [],
  breadcrumb: []
})

const handleCollect = () => {
  toggleCollect({
    content_id: info.value.detail.id
  }).then(res=>{
    info.value.detail.is_collected = res.data.is_collected
    info.value.detail.collect_count = res.data.collect_count
      ElMessage.success(res.msg)
  })
}

const getDetails = () => {
  contentDetail({
    id: route.query.id,
  }).then(res => {
    info.value = res.data
  })
}
watch(() => route.query.id, (newId, oldId) => {
  if (newId !== oldId) {
    getDetails()
  }
})
onMounted(() => {
  getDetails()
})
</script>

<template>
  <div class="main">
    <Header/>
    <div class="introduce">
      <div class="container">
        <div class="box bg-white flex justify-between">
          <div class="left hidden">
            <el-avatar :size="140" :src="isHttpOrHttps(info.detail.avatar_url)" />
          </div>
          <div class="right flex-1">
            <div class="title flex align-center">
              <div class="name">
                <span>{{info.detail.title}}</span>
              </div>
              <div class="tags">
                <span>{{info.detail.expert_number ?? '-'}}</span>
              </div>
            </div>
            <div class="desc">
              <span>{{info.detail.summary}}</span>
            </div>
            <div class="tag-list flex">
              <div class="tag" v-for="(item, index) in info.detail.research_area?.split(',')">
                <span>{{item}}</span>
              </div>
            </div>
            <shareCard :title="info.detail.title" :image="isHttpOrHttps(info.detail.avatar_url)">
              <div class="tool">
                <el-button type="primary" @click="jumpOutWeb(systemStore.kefu_url)">立即咨询</el-button>
                <el-button color="#EEAF3D" :icon="info.detail?.is_collected ? StarFilled : Star" @click="handleCollect">收藏</el-button>
              </div>
            </shareCard>
          </div>
        </div>
      </div>
    </div>
    <div class="contents">
      <div class="container flex justify-between">
        <div class="info flex-1">
          <div class="content bg-white">
            <template v-if="info.detail.academic_achievements">
              <div class="name-bar flex align-center">
                <img src="@/assets/images/common/icon_item.png" alt="">
                <span>学术成果</span>
              </div>
              <div class="values">
                <span>{{info.detail.academic_achievements}}</span>
              </div>
            </template>
            <template v-if="info.detail.tutoring_experience">
              <div class="name-bar flex align-center">
                <img src="@/assets/images/common/icon_item.png" alt="">
                <span>辅导经验</span>
              </div>
              <div class="values">
                <span>{{info.detail.tutoring_experience}}</span>
              </div>
            </template>
            <template v-if="info.detail.expertise_skills">
              <div class="name-bar flex align-center">
                <img src="@/assets/images/common/icon_item.png" alt="">
                <span>擅长技能</span>
              </div>
              <div class="values">
                <span>{{info.detail.expertise_skills}}</span>
              </div>
            </template>
            <template v-if="info.detail.service_types">
              <div class="name-bar flex align-center">
                <img src="@/assets/images/common/icon_item.png" alt="">
                <span>服务类型</span>
              </div>
              <div class="values">
                <span>{{info.detail.service_types ?? '-'}}</span>
              </div>
            </template>
          </div>
          <div class="about" v-if="info.related.services?.length">
            <aboutCard title="热门服务" :icon="getAssetsFile('common/icon_service.png')" moreUrl="/expert">
              <div class="items">
                <serviceCard :info="item" v-for="(item, index) in info.related.services" :key="index" />
              </div>
            </aboutCard>
          </div>
          <div class="about" v-if="info.related.journals?.length">
            <aboutCard title="相关书籍" :icon="getAssetsFile('common/icon_books.png')" moreUrl="/journal">
              <div class="items">
                <JournalCard :info="item" v-for="(item, index) in info.related.journals" :key="index" />
              </div>
            </aboutCard>
          </div>
        </div>
        <div class="slider">
          <div class="card">
            <friendCard />
          </div>
          <div class="card">
            <otherCard title="相关推荐" :icon="getAssetsFile('common/icon_zan.png')" more-url="/expert">
              <div class="users">
                <div class="user cursor-pointer flex align-center justify-between position-relative" v-for="(item, index) in info.recommended" :key="index" @click="safePush({path:'/expert/details',query:{id:item.id}})">
                  <div class="_left flex">
                    <el-avatar :size="56" :src="isHttpOrHttps(item.avatar_url)" />
                  </div>
                  <div class="_right flex-1">
                    <div class="name">
                      <span>{{item.title}}</span>
                    </div>
                    <div class="job">
                      <span>{{item.summary}}</span>
                    </div>
                    <div class="job">
                      <span>{{item.research_area}}</span>
                    </div>
                  </div>
                  <div class="tag position-absolute">
                    <span>{{item.expert_number ?? '-'}}</span>
                  </div>
                </div>
              </div>
            </otherCard>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"></div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main {
  background-color: #F4F6F9;
  .introduce{
    .container{
      .box{
        margin: px2rem(24) 0 px2rem(20);
        box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
        border-radius: px2rem(16);
        padding: px2rem(36) px2rem(24);
        box-sizing:  border-box;
        .left{
          width: px2rem(140);
          height: px2rem(140);
          border-radius: 50%;
          img{
            width: 100%;
          }
        }
        .right{
          margin-left: px2rem(20);
          .title{
            .name{
              font-size: px2rem(24);
              color: #2D2B32;
              line-height: 1;
              font-weight: 600;
            }
            .tags{
              background: linear-gradient( 90deg, #F8F0DF 0%, #EFE3C8 100%);
              margin-left: px2rem(10);
              border-radius: px2rem(6);
              padding: px2rem(5) px2rem(10);
              box-sizing: border-box;
              color: #452111;
              font-size: px2rem(14);
              font-weight: 500;
            }
          }
          .desc{
            margin: px2rem(10) 0 px2rem(8);
            color: #535871;
            font-size: px2rem(16);
            line-height: px2rem(22);
          }
          .tag-list{
            flex-wrap: wrap;
            .tag{
              border: px2rem(1) solid #CCCCCC;
              border-radius: px2rem(4);
              color: #434864;
              font-size:  px2rem(14);
              padding: px2rem(5) px2rem(8);
              margin-bottom: px2rem(10);
              &+.tag{
                margin-left: px2rem(8);
              }
            }
          }
          .tool{
            .el-button{
              &:first-child{
                width: px2rem(182);
                height: px2rem(52);
                font-size: px2rem(20);
                border-radius: px2rem(27);
              }
              &:last-child{
                background-color: transparent;
                width: px2rem(145);
                height: px2rem(52);
                font-size: px2rem(20);
                border-radius: px2rem(26);
                color: #EEAF3D;
              }
            }
          }
        }
      }
    }
  }
  .contents{
    .container{
      .info{
        margin-right: px2rem(20);
        .content{
          box-shadow: 0 px2rem(2) px2rem(6) 0 rgba(8,48,123,0.06);
          border-radius:  px2rem(16);
          padding: 0 px2rem(20) px2rem(24);
          box-sizing: border-box;
          .name-bar{
            color: #191B1F;
            font-weight: 600;
            font-size: px2rem(24);
            padding: px2rem(30) 0;
            box-sizing: border-box;
            img{
              width: px2rem(39);
              height: px2rem(27);
              margin-right: px2rem(10);
            }
          }
          .values{
            color: #525252;
            font-size: px2rem(18);
            line-height: px2rem(25);
          }
        }
        .about{
          margin-top: px2rem(60);

          .items{
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: px2rem(16);
          }
        }
      }
      .slider{
        width: px2rem(353);
        .card{
          &+.card{
            margin-top: px2rem(20);
          }
          .users{
            .user{
              background: #F4F6F9;
              box-shadow: 0 px2rem(2) px2rem(8) 0 rgba(0,66,190,0.08);
              border-radius: px2rem(6);
              padding: px2rem(26) px2rem(22);
              box-sizing: border-box;
              &+.user{
                margin-top: px2rem(20);
              }
              ._left{
                img{
                  width: px2rem(56);
                  height: px2rem(56);
                  border-radius: 50%;
                }
              }
              ._right{
                margin-left: px2rem(10);
                .name{
                  color: #2D2B32;
                  font-weight: 600;
                  font-size: px2rem(20);
                  line-height: 1;
                }
                .job{
                  color: #535871;
                  font-weight: 600;
                  font-size: px2rem(14);
                  line-height: 1;
                  margin-top: px2rem(10);
                }
              }
              .tag{
                top: 0;
                right: 0;
                color: #452111;
                font-size: px2rem(14);
                font-weight: 500;
                line-height: 1;
                padding: px2rem(8) px2rem(10);
                background: linear-gradient( 90deg, #F8F0DF 0%, #EFE3C8 100%);
                border-radius: 0 px2rem(6) 0 px2rem(6);
              }
            }
          }
        }
      }
    }
  }
  .empty{
    margin-top: px2rem(100);
  }
}
</style>