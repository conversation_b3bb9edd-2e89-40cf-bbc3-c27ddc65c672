<script setup lang="ts">
import {isHttpOrHttps} from "@/utils/utils.ts";

const { info } = defineProps({
  info: {
    type: Object,
    required: true,
  }
});
import {safePush} from "@/utils/router.ts";
</script>

<template>
  <div class="item bg-white hidden cursor-pointer" @click="safePush({path: '/service/details', query: {id: info.id}})">
    <div class="thumb">
      <el-image :src="isHttpOrHttps(info.cover_image)" fit="cover"></el-image>
    </div>
    <div class="info bg-white">
      <div class="title">
        <span v-html="info.title"></span>
      </div>
      <div class="desc flex justify-between row1">
        <span>{{info.subtitle}}</span>
      </div>
      <div class="prices flex align-center justify-between">
        <template v-if="info.price > 0">
          <div class="price">
            <span>¥ {{info.price}}</span>
          </div>
        </template>
        <template v-else>
          <div class="ask">
            <el-button link type="primary">咨询</el-button>
          </div>
        </template>
        <div class="users">
          <span>{{info.purchase_count}}人购买</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item {
  box-shadow: 0 px2rem(2) px2rem(21) 0 rgba(0, 66, 190, 0.14);
  border-radius: px2rem(24);
  width: 100%;

  .thumb {
    height: px2rem(180);
    .el-image{
      width: 100%;
      height: 100%;
    }
  }

  .info {
    padding: px2rem(16);
    box-sizing: border-box;

    .title {
      font-size: px2rem(22);
      font-weight: 500;
    }

    .desc {
      margin-top: px2rem(8);
      color: #535871;
      font-size: px2rem(16);
    }

    .prices {
      margin-top: px2rem(20);

      .ask{
        .el-button{
          font-size: px2rem(22);
        }
      }

      .price {
        color: #E64F26;
        font-size: px2rem(22);
        font-weight: 500;
      }

      .users {
        color: #696A77;
        font-size: px2rem(16);
      }
    }
  }
}
</style>