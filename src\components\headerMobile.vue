<script setup lang="ts">
import { useUserInfoStore } from '@/store/userInfo.ts'
import { useSystemStore } from '@/store/system.ts'
import {isHttpOrHttps} from "@/utils/utils.ts";
import {safePush} from "@/utils/router.ts";
import { useRoute } from "vue-router";
const userInfoStore = useUserInfoStore()
const systemStore = useSystemStore()
const route = useRoute();
const handleLogin = () => {
  window.showLoginMobile()
}
</script>

<template>
  <header class="flex justify-between align-center">
    <div class="logo" @click="safePush('/')">
      <img class="flex" src="@/assets/images/common_mobile/logo.png" alt="">
    </div>
    <div class="login flex align-center">
      <div class="search" @click="safePush('/search')">
        <img class="flex" src="@/assets/images/common_mobile/icon_search.png" alt="">
      </div>
      <div class="flex" v-if="systemStore.openMemberCenter">
        <template v-if="userInfoStore.isLogin">
          <el-avatar :size="30" :src="isHttpOrHttps(userInfoStore.userInfo.avatar)" @click="safePush('/user')" />
        </template>
        <template v-else>
          <div class="button">
            <button class="text-white" @click="handleLogin">登录/注册</button>
          </div>
        </template>
      </div>
    </div>
  </header>
  <div class="menus flex align-center justify-around bg-white">
    <div class="menu flex flex-column align-center" :class="{active: route.path.includes('/home')}" @click="safePush('/home')">
      <div class="name">主页</div>
      <div class="icon">
        <template v-if="route.path.includes('/home')">
          <img class="flex" src="@/assets/images/home_mobile/icon_menu_active.png" alt="">
        </template>
      </div>
    </div>
    <div class="menu flex flex-column align-center" :class="{active: route.path.includes(item.path)}" v-for="(item, index) in systemStore.rules" :key="index" @click="safePush(item.path)">
      <div class="name">{{ item.title }}</div>
      <div class="icon">
        <template v-if="route.path.includes(item.path)">
          <img class="flex" src="@/assets/images/home_mobile/icon_menu_active.png" alt="">
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
header{
  background: linear-gradient( 180deg, #3B78C8 0%, #5E90D2 100%);
  width: 100%;
  height: 54px;
  padding: 12px 15px;
  box-sizing: border-box;
  .logo{
    width: 90px;
    img{
      width: 100%;
    }
  }
  .login{
    .search{
      width: 20px;
      margin-right: 16px;
      img{
        width: 100%;
      }
    }
    .button{
      button{
        font-size: 12px;
        padding: 5px 8px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #FFFFFF;
        background: transparent;
      }
    }
  }
}
.menus{
  padding: 12px 15px 11px;
  box-sizing: border-box;
  box-shadow: 0 2px 13px 0 rgba(22,80,141,0.06);
  .menu {
    font-size: 13px;
    color: #535871;
    &.active{
      color: $maincolor;
    }
    .name{
      line-height: 1;
    }
    .icon{
      width: 22px;
      height: 5px;
      margin-top: 5px;
      img{
        width: 100%;
      }
    }
  }
}
</style>