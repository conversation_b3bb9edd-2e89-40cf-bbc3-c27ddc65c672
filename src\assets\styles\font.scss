@use "utils.scss" as *;
$fonts: 'YouSheBiaoTiYuan';
@each $font in $fonts {
  @font-face {
    font-display: swap;
    font-family: $font;
    src: url('../fonts/#{$font}.otf') format('truetype');
  }
}

@for $i from 12 through 100 {
  @if $i % 2 == 0 {
    .lh-#{$i} {
      line-height: px2rem($i);
    }

    .fs-#{$i} {
      font-size: px2rem($i);
    }
  }
}

.fc-white {
  color: #fff;
}

.fc-green {
  color: #26E3C1;
}

.fc-red {
  color: #D34F4F;
}

.fc-blue {
  color: #52B9FB;
}

.fc-blue-light {
  color: #BBDAFB;
}

.fc-orange {
  color: #FDAC37;
}


