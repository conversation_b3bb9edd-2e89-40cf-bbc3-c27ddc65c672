<script setup lang="ts">
import {safePush} from "@/utils/router.ts";
const { info } = defineProps({
  info: {
    type: Object,
    required: true,
  }
});
</script>

<template>
  <div class="item cursor-pointer bg-white" @click="safePush({path: '/stories/details', query:{id: info.id}})">
    <div class="title row2">
      <span v-html="info.title"></span>
    </div>
    <div class="tags flex align-center">
      <div class="tag">
        <span>{{info.author}}</span>
      </div>
      <div class="time">
        <span>{{info.publish_date}}</span>
      </div>
    </div>
    <div class="desc row3">
      <span>{{info.summary}}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.item{
  border: 1px solid #E6EBF0;
  border-radius: 12px;
  padding: 10px 5px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  .title{
    color: #191B1F;
    font-size: 14px;
    line-height: 20px;
    font-weight: bold;
  }
  .tags{
    margin: 4px 0 6px;
    .tag{
      color: #3670EE;
      background-color: #EBF1FD;
      font-size: 12px;
      border-radius: 2px;
      line-height: 1;
      padding: 3px 5px;
      box-sizing: border-box;
      display: inline-block;
    }
    .time{
      color: #191B1F;
      font-size: 12px;
      margin: 0 12px;
    }
  }
  .desc{
    font-size: 12px;
    color: #191B1F;
    word-break: break-word;
  }
}
</style>