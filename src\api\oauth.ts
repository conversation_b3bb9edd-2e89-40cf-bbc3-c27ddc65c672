import myAxios from '@/api/axios'

const controllerUrl = '/api/OAuthLogin/'

/**
 * 获取可用的第三方登录平台列表
 */
export function oauthList() {
  return myAxios({
    url: controllerUrl + 'oauthList',
    method: 'get',
  })
}

/**
 * 处理第三方登录回调
 */
export function loginAgent(code: string, state: string) {
  return myAxios({
    url: controllerUrl + 'loginAgent',
    method: 'get',
    params: {
      code: code,
      state: state,
    },
  }, {
    loading: true,
  })
}

/**
 * 获取第三方账号绑定列表
 */
export function bindList() {
  return myAxios({
    url: controllerUrl + 'bindList',
    method: 'get',
  })
}

/**
 * 解绑第三方账号
 */
export function unbind(data: any) {
  return myAxios({
    url: controllerUrl + 'unbind',
    method: 'post',
    data: data,
  }, {
    loading: true,
  })
}

/**
 * 获取修改密码页面信息
 */
export function getChangePassword() {
  return myAxios({
    url: controllerUrl + 'changePassword',
    method: 'get',
  })
}

/**
 * 提交修改密码
 */
export function postChangePassword(params: any) {
  return myAxios({
    url: controllerUrl + 'changePassword',
    method: 'post',
    data: params,
  }, {
    loading: true,
  })
} 