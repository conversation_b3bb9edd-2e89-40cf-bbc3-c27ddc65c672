<script setup lang="ts">
import {ref, onMounted} from "vue";
import { useRoute } from "vue-router";
import { safePush } from "@/utils/router.ts";
import { isHttpOrHttps } from '@/utils/utils.ts';
import { useUserInfoStore } from '@/store/userInfo.ts'
import { useSystemStore } from '@/store/system.ts'
const userInfoStore = useUserInfoStore()
const systemStore = useSystemStore()

const route = useRoute();
const keywords = ref('')

const handleLogin = () => {
  window.showLogin()
}

const logout = () => {
  userInfoStore.setUserInfo({})
  userInfoStore.setIsLogin(false);
  if (route.path.includes('/user')) {
    safePush('/')
  }
}
const emits = defineEmits(["search"]);
const handleSearch = () => {
  if (route.path.includes('/search')) {
    // 当前已经在搜索页
    emits('search', keywords.value);
  } else {
    // 跳转到搜索页
    safePush({
      path:'/search',
      query:{
        keyword: keywords.value
      }
    })
  }
}
onMounted(() => {
  if (route.query.keyword) {
    keywords.value = route.query.keyword as string
  }
})
</script>

<template>
  <header class="bg-white">
    <div class="container flex justify-between">
      <div class="logo cursor-pointer" @click="safePush('/')">
        <img src="@/assets/images/common/logo.png" alt="">
      </div>
      <div class="center">
        <div class="search flex align-center justify-between hidden">
          <el-input class="flex-1" v-model="keywords" placeholder="请输入" @keydown.enter="handleSearch" />
          <el-button color="var(--maincolor)" @click="handleSearch">搜索</el-button>
        </div>
        <div class="menus flex align-center justify-between">
          <div class="menu cursor-pointer flex flex-column align-center" :class="{active: route.path.includes('/home')}" @click="safePush('/home')">
            <div class="name">主页</div>
            <div class="icon">
              <template v-if="route.path.includes('/home')">
                <img class="flex" src="@/assets/images/home_mobile/icon_menu_active.png" alt="">
              </template>
            </div>
          </div>
          <div class="menu cursor-pointer flex flex-column align-center" :class="{active: route.path.includes(item.path)}" v-for="(item, index) in systemStore.rules" :key="index" @click="safePush(item.path)">
            <div class="name">{{ item.title }}</div>
            <div class="icon">
              <template v-if="route.path.includes(item.path)">
                <img class="flex" src="@/assets/images/home_mobile/icon_menu_active.png" alt="">
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="login" v-if="systemStore.openMemberCenter">
        <template v-if="userInfoStore.isLogin">
          <el-dropdown>
            <el-avatar class="cursor-pointer" :size="60" :src="isHttpOrHttps(userInfoStore.userInfo.avatar)" @click="safePush('/user')" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button @click="handleLogin">登录/注册</el-button>
        </template>
      </div>
    </div>
  </header>
</template>

<style scoped lang="scss">
header {
  width: 100%;
  height: px2rem(166);

  .container {
    height: 100%;

    .logo {
      margin-top: px2rem(38);

      img {
        width: px2rem(197);
        height: px2rem(66);
      }
    }

    .center {
      margin-top: px2rem(38);
      width: px2rem(600);

      .search {
        width: 100%;
        height: px2rem(43);
        border-radius: px2rem(8);
        border: px2rem(1) solid $maincolor;
        padding: 0 px2rem(1);
        box-sizing: border-box;

        :deep(.el-input) {
          border: none;
          font-size: px2rem(18);

          .el-input__wrapper {
            box-shadow: none;
          }
        }

        .el-button {
          font-size: px2rem(18);
          font-weight: normal;
        }
      }

      .menus {
        margin-top: px2rem(37);
        .menu{
          font-size: px2rem(20);
          color: #1E1E1E;
          &.active{
            color: $maincolor;
          }
          .name{
            line-height: 1;
          }
          .icon{
            width: px2rem(30);
            height: px2rem(5);
            margin-top: px2rem(5);
            img{
              width: 100%;
            }
          }
        }
      }
    }

    .login {
      margin-top: px2rem(46);

      .el-button {
        font-size: px2rem(18);
        font-weight: normal;
        border-color: $maincolor;
        color: $maincolor;
        width: px2rem(140);
        height: px2rem(50);
        padding: 0;
      }
    }
  }
}
</style>