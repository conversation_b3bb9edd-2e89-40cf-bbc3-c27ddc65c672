<script setup lang="ts">
import {ref, onMounted, onUnmounted} from 'vue'
import {getAssetsFile, isHttpOrHttps, jumpOutWeb} from "@/utils/utils.ts";
import {ArrowRight} from '@element-plus/icons-vue'
import {vue3ScrollSeamless} from "vue3-scroll-seamless";
import { useSystemStore } from '@/store/system.ts'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import ServiceCard from "@/components/serviceCard.vue";
import ExpertCard from "@/components/expertCard.vue";
import JournalCard from "@/components/journalCard.vue";
import {safePush} from "@/utils/router.ts";
const systemStore = useSystemStore();
const heightimg = ref('0px')
const classOptions = {
  limitMoveNum: 4,
  direction: 2,
  step: 0.5,
  hoverStop: true
};
const customerOutcomes = ref([1,2,3,4,5,6,7,8,9,10])
const tabs = ref(['推荐服务', '推荐专家', '推荐期刊'])
const tabCur = ref(0)
const services = ref([
  {
    name: '本科阶段',
    desc: '科研入门引导、学业规划制定、基础技能训练，夯实科研基础，增强升学就业核心竞争力',
    more: '了解更多服务',
    icon: getAssetsFile('home/service_1.png'),
    url: '',
    backgroundColor: '#D6F0FF'
  },
  {
    name: '硕士阶段',
    desc: '论文写作攻坚、研究成果产出、发展方向明晰，保障论文通过，强化申博就业硬核实力',
    more: '了解更多服务',
    icon: getAssetsFile('home/service_2.png'),
    url: '',
    backgroundColor: '#D3DBFE'
  },
  {
    name: '博士阶段',
    desc: '核心成果培育、学术资源对接，加速成长进程，构筑职业发展高端优势',
    more: '了解更多服务',
    icon: getAssetsFile('home/service_3.png'),
    url: '',
    backgroundColor: '#FFD5FC'
  },
  {
    name: '在职阶段',
    desc: '职称晋升规划、学用知识融合、前沿动态传递，助力职称提升，以学术推动职业进阶',
    more: '了解更多服务',
    icon: getAssetsFile('home/service_4.png'),
    url: '',
    backgroundColor: '#E7D9FF'
  },
])

const handleResize = ()=>{
  heightimg.value = document.getElementsByClassName('imgs')[0].offsetHeight + 'px'
}
onMounted(() => {
  setTimeout(()=>{
    heightimg.value = document.getElementsByClassName('imgs')[0].offsetHeight + 'px'
  },500)
  window.addEventListener('resize',handleResize)
})
onUnmounted(()=>{
  window.removeEventListener('resize',handleResize)
})
</script>

<template>
  <div class="main bg-white">
    <Header/>
    <div class="banners">
      <el-carousel motion-blur :interval="10000" :height="heightimg">
        <el-carousel-item v-for="(item, index) in systemStore.site.banner" :key="index">
          <img class="imgs" :src="isHttpOrHttps(item)" alt="" style="width: 100%">
        </el-carousel-item>
      </el-carousel>
    </div>
    <div class="recommend">
      <div class="container">
        <div class="card-menus flex align-center justify-between">
          <el-link underline="never" class="menu" v-for="(item, index) in systemStore.site.category" :key="index" @click="safePush({path: '/service/index', query: {category_id: item.id}})">
            <el-image :src="isHttpOrHttps(item.image)"></el-image>
          </el-link>
        </div>
        <div class="tabs flex align-center justify-center">
          <div class="tab cursor-pointer" :class="{active: tabCur === index}" v-for="(item, index) in tabs" :key="index"
               @click="tabCur = index">
            <span>{{ item }}</span>
          </div>
        </div>
        <div class="items">
          <template v-if="tabCur === 0">
            <ServiceCard :info="item" v-for="(item, index) in systemStore.site.recommended.services" :key="index"/>
          </template>
          <template v-else-if="tabCur === 1">
            <ExpertCard :info="item" v-for="(item, index) in systemStore.site.recommended.experts" :key="index" />
          </template>
          <template v-else-if="tabCur === 2">
            <JournalCard :info="item" v-for="(item, index) in systemStore.site.recommended.journals" :key="index" />
          </template>
        </div>
      </div>
    </div>
    <div class="support">
      <div class="container">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab1_title}}</span>
        </div>
        <div class="desc text-center">
          <span>{{systemStore.site.home.tab1_desc}}</span>
        </div>
        <div class="img flex justify-center">
          <el-image :src="getAssetsFile('home/bg_support.png')"></el-image>
        </div>
      </div>
    </div>
    <div class="services">
      <div class="container position-relative">
        <div class="bg position-absolute">
          <el-image :src="getAssetsFile('home/bg_service.png')"></el-image>
        </div>
        <div class="title text-center position-relative">
          <span>{{systemStore.site.home.tab2_title}}</span>
        </div>
        <div class="desc text-center position-relative">
          <span>{{systemStore.site.home.tab2_desc}}</span>
        </div>
        <div class="service flex align-center justify-between position-relative">
          <div class="item flex flex-column align-center" :style="{backgroundColor: item.backgroundColor}"
               v-for="(item, index) in services" :key="index">
            <el-image :src="item.icon"></el-image>
            <div class="name">
              <span>{{ item.name }}</span>
            </div>
            <div class="description text-center">
              <span>{{ item.desc }}</span>
            </div>
            <el-link underline="never" class="more flex align-center" @click="safePush('/service')">
              <span>{{ item.more }}</span>
              <el-icon>
                <ArrowRight/>
              </el-icon>
            </el-link>
          </div>
        </div>
      </div>
    </div>
    <div class="study">
      <div class="container">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab3_title}}</span>
        </div>
        <div class="description text-center">
          <span>{{systemStore.site.home.tab3_desc}}</span>
        </div>
        <div class="box flex justify-between">
          <div class="left">
            <div class="item hidden position-relative cursor-pointer"
                 :style="{background: `url(${isHttpOrHttps(item.cover_image)}) no-repeat`, backgroundSize: '100% 100%'}"
                 v-for="(item, index) in systemStore.site.HEADLINE.featured" :key="index" @click="safePush({path: '/stories/details', query:{id: item.id}})">
              <div class="name row1 text-white position-absolute">
                <span>{{item.title}}</span>
              </div>
            </div>
          </div>
          <div class="right">
            <div class="title flex align-center">
              <el-image :src="getAssetsFile('home/icon_fire.png')"></el-image>
              <span>热门文章</span>
            </div>
            <div class="ranks">
              <el-link underline="never" class="rank flex align-center justify-start" v-for="(item, index) in systemStore.site.HEADLINE.list" :key="index" @click="safePush({path: '/stories/details', query:{id: item.id}})">
                <span class="text-center">{{ index + 1 }}</span>
                <span class="row1">{{item.title}}</span>
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="achievement">
      <div class="container">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab5_title}}</span>
        </div>
        <div class="desc text-center">
          <span>{{systemStore.site.home.tab5_desc}}</span>
        </div>
        <vue3ScrollSeamless
            class="hidden"
            :classOptions="classOptions"
            :dataList="customerOutcomes"
        >
          <div class="items flex align-center">
            <div class="item position-relative" v-for="(item, index) in systemStore.site.customer_result" :key="index">
              <div class="tag position-absolute">
                <img src="@/assets/images/home/<USER>" alt="">
              </div>
              <div class="name flex align-center">
                <img src="@/assets/images/home/<USER>" alt="">
                <span>客户成果</span>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>期刊名称</span>
                </div>
                <div class="value flex-1">
                  <span>《{{item.journal_name ?? '-'}}》</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>影响因子</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.impact_factor ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>作者信息</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.author_info ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>服务项目</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.service_project ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>成果描述</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.description ?? '-'}}</span>
                </div>
              </div>
              <div class="part flex">
                <div class="_name">
                  <span>发表日期</span>
                </div>
                <div class="value flex-1">
                  <span>{{item.publication_date ?? '-'}}</span>
                </div>
              </div>
            </div>
          </div>
        </vue3ScrollSeamless>
      </div>
    </div>
    <div class="school">
      <div class="container">
        <div class="title text-center">
          <span>{{systemStore.site.home.tab4_title}}</span>
        </div>
        <div class="desc text-center">
          <span>{{systemStore.site.home.tab4_desc}}</span>
        </div>
        <div class="links">
          <div class="link cursor-pointer hidden" v-for="(item, index) in systemStore.site?.firind_link" :key="index">
            <el-image :src="isHttpOrHttps(item.image)" fit="scale-down" @click="jumpOutWeb(item.url)"></el-image>
          </div>
        </div>
      </div>
    </div>
    <Footer/>
  </div>
</template>

<style scoped lang="scss">
.main {

  .banners {
    width: 100%;
  }

  .recommend {
    padding: px2rem(50) 0 px2rem(80);
    box-sizing: border-box;

    .card-menus {
      margin-bottom: px2rem(80);

      .menu {
        .el-image {
          width: px2rem(186);
          height: px2rem(105);
        }
      }
    }

    .tabs {
      .tab {
        color: #434864;
        font-size: px2rem(26);
        padding-bottom: px2rem(6);
        box-sizing: border-box;
        font-weight: 400;
        border-bottom: 2px solid transparent;

        &.tab {
          margin-left: px2rem(46);
        }

        &.active {
          color: $maincolor;
          border-color: $maincolor;
        }
      }
    }

    .items {
      margin-top: px2rem(52);
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: px2rem(40) px2rem(20);
    }
  }

  .support {
    background-color: #FAFAFA;
    width: 100%;

    .container {
      padding: px2rem(80) 0 px2rem(102);
      box-sizing: border-box;

      .title {
        color: $maincolor;
        font-size: px2rem(36);
        font-weight: bold;
      }

      .desc {
        width: px2rem(1000);
        margin: px2rem(26) auto px2rem(68);
        font-size: px2rem(18);
        color: #909090;
        line-height: px2rem(30);
        font-weight: 400;
      }

      .el-image {
        width: px2rem(1068);
        height: px2rem(710);
      }
    }
  }

  .services {
    .container {
      padding: px2rem(100) 0 px2rem(80);
      box-sizing: border-box;

      .bg {
        z-index: 1;
        top: px2rem(223);
        width: 100%;
        height: px2rem(544);
      }

      .title {
        font-size: px2rem(36);
        font-weight: bold;
        color: $maincolor;
        z-index: 2;
      }

      .desc {
        width: px2rem(800);
        font-size: px2rem(18);
        color: #909090;
        margin: px2rem(26) auto px2rem(70);
        line-height: px2rem(30);
        z-index: 2;
      }

      .service {
        z-index: 2;
        .item {
          width: px2rem(277);
          height: px2rem(424);
          border-radius: px2rem(24);
          padding: 0 px2rem(24);
          box-sizing: border-box;

          .el-image {
            width: px2rem(184);
            height: px2rem(184);
          }

          .name {
            color: #1F1F1F;
            font-size: px2rem(36);
          }

          .description {
            font-size: px2rem(20);
            color: #5A5A5A;
            //height: px2rem(84);
            margin: px2rem(16) 0 px2rem(12);
          }

          .more {
            font-size: px2rem(20);
            color: #909090;
            font-weight: normal;
          }
        }
      }
    }
  }

  .study {
    background-color: #FAFAFA;
    width: 100%;

    .container {
      padding: px2rem(80) 0 px2rem(100);

      .title {
        color: $maincolor;
        font-size: px2rem(36);
        font-weight: bold;
      }

      .description {
        font-size: px2rem(20);
        color: #909090;
        margin: px2rem(26) 0 px2rem(70);
      }

      .box {
        .left {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          grid-gap: px2rem(20);

          .item {
            background: $maincolor;
            width: px2rem(339);
            height: px2rem(250);
            border-radius: px2rem(24);

            .name {
              left: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.6);
              height: px2rem(46);
              line-height: px2rem(46);
              font-size: px2rem(18);
              padding: 0 px2rem(12);
              box-sizing: border-box;
              width: 100%;
            }
          }
        }

        .right {
          box-shadow: 0 px2rem(2) px2rem(18) 0 rgba(66, 88, 241, 0.1);
          border-radius: px2rem(24);
          width: px2rem(471);
          height: px2rem(520);
          padding: px2rem(30);
          box-sizing: border-box;

          .title {
            padding-bottom: px2rem(30);
            border-bottom: px2rem(1) solid #E4E6EA;
            color: $maincolor;

            .el-image {
              width: px2rem(40);
              height: px2rem(40);
              margin-right: px2rem(16);
              font-size: px2rem(30);
            }
          }

          .ranks {
            padding-top: px2rem(20);
            box-sizing: border-box;

            .rank {
              font-size: px2rem(18);

              & + .rank {
                margin-top: px2rem(24);
              }

              span {
                font-weight: normal;

                &:first-child {
                  color: #C4C4C9;
                  padding-right: px2rem(10);
                  font-family: YouSheBiaoTiYuan, serif;
                  width: px2rem(14);
                }
              }

              &:nth-child(1) {
                span {
                  &:first-child {
                    color: #D6635E;
                  }
                }
              }

              &:nth-child(2) {
                span {
                  &:first-child {
                    color: #E59A6B;
                  }
                }
              }

              &:nth-child(3) {
                span {
                  &:first-child {
                    color: #F1C363;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .achievement {
    .container {
      padding: px2rem(100) 0;
      box-sizing: border-box;

      .title {
        color: $maincolor;
        font-size: px2rem(36);
        font-weight: bold;
      }

      .desc {
        font-size: px2rem(20);
        color: #909090;
        margin: px2rem(26) 0 px2rem(70);
      }

      .items {
        margin: px2rem(2) 0;
        .item {
          box-shadow: 0 px2rem(1) px2rem(13) 0 rgba(66, 88, 241, 0.14);
          border-radius: px2rem(24);
          width: px2rem(285);
          padding: px2rem(20) px2rem(18);
          box-sizing: border-box;
          margin-right: px2rem(20);

          .tag {
            top: 0;
            right: px2rem(18);
            width: px2rem(32);
            height: px2rem(49);
            img{
              width: 100%;
            }
          }

          .name {
            font-size: px2rem(28);
            margin-bottom: px2rem(19);

            img {
              width: px2rem(28);
              height: px2rem(28);
              margin-right: px2rem(14);
            }
          }

          .part {
            & + .part {
              margin-top: px2rem(6);
            }

            ._name {
              font-size: px2rem(14);
              color: #909090;
              width: px2rem(60);
              margin-right: px2rem(10);
            }

            .value {
              font-size: px2rem(14);
              word-break: break-all;
            }
          }
        }
      }
    }
  }

  .school {
    background-color: #FAFAFA;

    .container {
      padding: px2rem(80) 0 px2rem(100);
      box-sizing: border-box;

      .title {
        color: $maincolor;
        font-size: px2rem(36);
      }

      .desc {
        margin-top: px2rem(26);
        color: #909090;
        font-size: px2rem(20);
      }

      .links {
        margin-top: px2rem(70);
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        grid-gap: px2rem(30) px2rem(16);

        .link {
          width: px2rem(186);
          height: px2rem(80);
          box-shadow: 0 px2rem(7) px2rem(16) 0 rgba(66, 88, 241, 0.12);
          border-radius: px2rem(8);
        }
      }
    }
  }
}

</style>