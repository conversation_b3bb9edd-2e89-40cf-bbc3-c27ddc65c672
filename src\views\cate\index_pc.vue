<script setup lang="ts">
import {ref, onMounted, watch, computed} from 'vue'
import Header from "@/components/header.vue";
import Footer from "@/components/footer.vue";
import { useRoute, useRouter } from 'vue-router'
import { useSystemStore } from '@/store/system.ts'
const systemStore = useSystemStore()
const route = useRoute();
const router = useRouter();
const menus = ref(systemStore.hiddenNav)
const menuCur = ref()
const details = computed(() => {
  return menus.value.find((item) => item.title === menuCur.value)
})
onMounted(() => {
  menuCur.value = menus.value.length ? menus.value[0]?.title : '';
})
watch(menuCur, (val) => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      name:val
    }
  })
})
watch(() => route.query.name, (newVal) => {
  console.log(newVal)
  menuCur.value = newVal as string
}, {
  deep: true,
  immediate: true,
})
</script>

<template>
  <div class="main bg-white">
    <Header/>
    <div class="box">
      <div class="container flex justify-between">
        <div class="slide bg-white">
          <div class="menus">
            <div class="menu cursor-pointer" :class="{active: menuCur === item.title}" v-for="(item, index) in menus" :key="index" @click="menuCur = item.title">{{item.title}}</div>
          </div>
        </div>
        <div class="content flex-1 bg-white">
          <div class="title text-center">
            <span>{{details?.title}}</span>
          </div>
          <div class="body" v-html="details?.content"></div>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped lang="scss">
.main{
  background: #F4F6F9;
  .box{
    margin: px2rem(20) 0 px2rem(100);
    .container{
      .slide{
        width: px2rem(238);
        min-height: px2rem(600);
        border-radius: px2rem(16);
        .menus{
          padding: px2rem(22) px2rem(20);
          .menu{
            font-size: px2rem(20);
            color: #535871;
            font-weight: 500;
            padding: px2rem(10) px2rem(20);
            box-sizing:  border-box;
            border-radius: px2rem(8);
            &+.menu{
              margin-top: px2rem(20);
            }
            &.active{
              color: #FFFFFF;
              background-color: $maincolor;
            }
          }
        }
      }
      .content{
        margin-left: px2rem(20);
        border-radius: px2rem(16);
        padding: px2rem(50);
        box-sizing: border-box;
        .title{
          font-weight: 600;
          font-size: px2rem(28);
          color: #191B1F;
          margin-bottom: px2rem(30);
        }
      }
    }
  }
}

</style>